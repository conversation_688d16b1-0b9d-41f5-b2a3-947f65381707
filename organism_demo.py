#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
有机体组件演示应用

模块描述: 展示新创建的有机体组件功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import streamlit as st
import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 导入组件
from components.organisms import AnalysisPanel, DashboardHeader, NavigationSidebar, ReportViewer

def main():
    """主应用"""
    st.set_page_config(
        page_title="有机体组件演示",
        page_icon="🧬",
        layout="wide"
    )
    
    # 初始化session state
    if 'current_page' not in st.session_state:
        st.session_state.current_page = '分析面板演示'
    
    # 用户信息
    user_info = {
        'name': '张三',
        'email': 'z<PERSON><PERSON>@example.com',
        'department': '技术部',
        'avatar': '👤'
    }
    
    # 导航配置
    navigation_config = NavigationSidebar.email_navigation()
    
    # 渲染导航侧边栏
    nav_result = NavigationSidebar.render(
        navigation_config=navigation_config,
        current_page=st.session_state.current_page,
        key="main_nav"
    )
    
    # 更新当前页面
    if nav_result['selected_page'] and nav_result['selected_page'] != st.session_state.current_page:
        st.session_state.current_page = nav_result['selected_page']
        st.rerun()
    
    # 渲染仪表盘头部
    header_result = DashboardHeader.render(
        title="🧬 有机体组件演示系统",
        subtitle="展示复杂功能组件的强大能力",
        user_info=user_info,
        key="main_header"
    )
    
    # 根据当前页面显示内容
    current_page = st.session_state.current_page
    
    if current_page == '分析面板演示' or current_page == '数据概览':
        demo_analysis_panel()
    elif current_page == '报告演示' or current_page == '分析报告':
        demo_report_viewer()
    elif current_page == '头部组件演示':
        demo_dashboard_header()
    elif current_page == '导航演示':
        demo_navigation_sidebar()
    else:
        demo_overview()

def demo_analysis_panel():
    """演示分析面板组件"""
    st.header("📊 分析面板组件演示")
    
    # 创建示例数据
    np.random.seed(42)
    email_data = pd.DataFrame({
        'sender': np.random.choice(['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'], 100),
        'email_type': np.random.choice(['工作报告', '会议通知', '项目更新', '日常沟通'], 100),
        'confidence': np.random.uniform(0.7, 1.0, 100),
        'date': pd.date_range('2024-01-01', periods=100, freq='D'),
        'attachment_count': np.random.poisson(2, 100),
        'word_count': np.random.normal(500, 200, 100).astype(int)
    })
    
    # 筛选配置
    filter_config = {
        'sender': {
            'type': 'multiselect',
            'label': '发件人',
            'options': email_data['sender'].unique().tolist(),
            'default': email_data['sender'].unique().tolist()
        },
        'email_type': {
            'type': 'selectbox',
            'label': '邮件类型',
            'options': ['全部'] + email_data['email_type'].unique().tolist(),
            'default': '全部'
        },
        'confidence_threshold': {
            'type': 'slider',
            'label': '置信度阈值',
            'min_value': 0.0,
            'max_value': 1.0,
            'default': 0.8
        }
    }
    
    # 渲染分析面板
    result = AnalysisPanel.render(
        data=email_data,
        title="📧 邮件数据分析面板",
        config={
            'filters': filter_config,
            'charts': [
                {
                    'type': 'pie',
                    'title': '邮件类型分布',
                    'config': {
                        'values': 'email_type',
                        'names': 'email_type'
                    }
                },
                {
                    'type': 'bar',
                    'title': '发件人邮件数量',
                    'config': {
                        'x': 'sender',
                        'y': 'attachment_count'
                    }
                }
            ]
        },
        key="email_analysis_panel"
    )
    
    # 显示分析结果
    if result['selected_rows']:
        st.success(f"已选择 {len(result['selected_rows'])} 行数据进行详细分析")

def demo_report_viewer():
    """演示报告查看器组件"""
    st.header("📋 报告查看器组件演示")
    
    # 创建示例报告数据
    report_data = {
        'title': '📧 邮件分析月度报告',
        'subtitle': '2024年1月邮件数据分析总结',
        'type': '月度分析报告',
        'author': 'AI分析系统',
        'created_date': datetime.now(),
        'summary': {
            'total_records': 1234,
            'completion_rate': 95.6,
            'anomalies': 12,
            'confidence': 87.3
        },
        'findings': [
            {
                'title': '邮件类型分布趋势',
                'description': '工作报告类邮件占比持续上升，达到45%，表明团队工作汇报机制日趋完善。',
                'importance': 'high',
                'recommendation': '建议建立标准化的工作报告模板，提高报告质量和一致性。'
            },
            {
                'title': '发件人活跃度分析',
                'description': '发现3位发件人的邮件频率显著高于平均水平，可能存在工作负荷不均的情况。',
                'importance': 'medium',
                'recommendation': '建议进行工作负荷重新分配，确保团队工作平衡。'
            },
            {
                'title': '邮件处理效率',
                'description': 'AI分析准确率达到87.3%，相比上月提升了3.2个百分点。',
                'importance': 'low',
                'recommendation': '继续优化AI模型，目标是达到90%以上的准确率。'
            }
        ],
        'charts': [
            {
                'type': 'line',
                'title': '每日邮件数量趋势',
                'data': {
                    'date': pd.date_range('2024-01-01', periods=31, freq='D').strftime('%m-%d').tolist(),
                    'count': np.random.poisson(40, 31).tolist()
                },
                'config': {
                    'x': 'date',
                    'y': 'count'
                }
            }
        ],
        'detailed_data': pd.DataFrame({
            'metric': ['总邮件数', '已分析', '准确分类', '需人工审核'],
            'value': [1234, 1180, 1030, 150],
            'percentage': [100.0, 95.6, 87.3, 12.7]
        })
    }
    
    # 渲染报告查看器
    result = ReportViewer.render(
        report_data=report_data,
        report_type="analysis",
        key="monthly_report"
    )
    
    # 显示交互结果
    if result['exported']:
        st.success("报告导出功能已触发")
    if result['bookmarked']:
        st.info("报告已添加到书签")

def demo_dashboard_header():
    """演示仪表盘头部组件"""
    st.header("🎯 仪表盘头部组件演示")
    
    st.subheader("1. 基础头部")
    DashboardHeader.render(
        title="数据分析系统",
        subtitle="智能邮件分析平台",
        user_info={
            'name': '李四',
            'email': '<EMAIL>',
            'department': '数据部'
        },
        key="basic_header"
    )
    
    st.subheader("2. 面包屑导航头部")
    breadcrumbs = [
        {'label': '首页', 'url': '/'},
        {'label': '邮件分析', 'url': '/email'},
        {'label': '数据概览', 'url': '/email/overview'}
    ]
    DashboardHeader.breadcrumb_header(
        breadcrumbs=breadcrumbs,
        current_page="详细分析",
        key="breadcrumb_demo"
    )
    
    st.subheader("3. 状态显示头部")
    DashboardHeader.status_header(
        title="系统监控",
        status="正常",
        last_update=datetime.now(),
        key="status_demo"
    )
    
    st.subheader("4. 操作按钮头部")
    actions = [
        {'label': '开始分析', 'type': 'primary'},
        {'label': '导出数据', 'type': 'secondary'},
        {'label': '设置', 'type': 'secondary'}
    ]
    action_result = DashboardHeader.action_header(
        title="邮件分析工具",
        actions=actions,
        key="action_demo"
    )
    
    if any(action_result.values()):
        clicked_actions = [action for action, clicked in action_result.items() if clicked]
        st.success(f"点击了操作: {', '.join(clicked_actions)}")

def demo_navigation_sidebar():
    """演示导航侧边栏组件"""
    st.header("🧭 导航侧边栏组件演示")
    
    st.info("导航侧边栏已在左侧显示，您可以：")
    st.markdown("""
    - 🔍 **搜索功能**: 在搜索框中输入关键词过滤导航项
    - ⭐ **收藏功能**: 点击星号按钮收藏常用页面
    - 📁 **分组导航**: 点击分组标题展开/折叠导航项
    - 🔗 **多级导航**: 某些项目有子菜单
    """)
    
    st.subheader("紧凑型导航演示")
    nav_config = NavigationSidebar.email_navigation()
    selected_page = NavigationSidebar.compact_navigation(
        navigation_config=nav_config,
        key="compact_demo"
    )
    
    if selected_page:
        st.success(f"选择了页面: {selected_page}")
    
    st.subheader("面包屑导航演示")
    current_path = ["首页", "邮件分析", "数据概览", "详细分析"]
    NavigationSidebar.breadcrumb_navigation(
        current_path=current_path,
        navigation_config=nav_config,
        key="breadcrumb_nav_demo"
    )

def demo_overview():
    """演示概览"""
    st.header("🧬 有机体组件系统概览")
    
    st.markdown("""
    ## 🎯 有机体组件特点
    
    有机体组件是由分子组件组合而成的复杂功能模块，具有以下特点：
    
    ### 📊 分析面板组件 (AnalysisPanel)
    - **功能**: 完整的数据分析界面
    - **特性**: 集成搜索、筛选、图表、表格等功能
    - **应用**: 邮件分析、数据探索、报告生成
    
    ### 🎯 仪表盘头部组件 (DashboardHeader)
    - **功能**: 统一的页面头部和导航
    - **特性**: 用户信息、通知、设置、搜索
    - **应用**: 系统头部、状态显示、操作入口
    
    ### 🧭 导航侧边栏组件 (NavigationSidebar)
    - **功能**: 多级导航和菜单系统
    - **特性**: 分组导航、搜索、收藏、面包屑
    - **应用**: 系统导航、功能入口、快捷操作
    
    ### 📋 报告查看器组件 (ReportViewer)
    - **功能**: 报告展示和交互
    - **特性**: 多种报告格式、导出、评论、分享
    - **应用**: 分析报告、数据展示、结果分享
    """)
    
    st.markdown("---")
    
    st.subheader("🚀 快速体验")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("📊 分析面板", use_container_width=True):
            st.session_state.current_page = '分析面板演示'
            st.rerun()
    
    with col2:
        if st.button("📋 报告查看", use_container_width=True):
            st.session_state.current_page = '报告演示'
            st.rerun()
    
    with col3:
        if st.button("🎯 头部组件", use_container_width=True):
            st.session_state.current_page = '头部组件演示'
            st.rerun()
    
    with col4:
        if st.button("🧭 导航组件", use_container_width=True):
            st.session_state.current_page = '导航演示'
            st.rerun()

if __name__ == "__main__":
    main()
