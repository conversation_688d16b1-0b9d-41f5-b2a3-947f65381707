#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据表格分子组件

模块描述: 提供统一的数据表格显示接口，支持筛选、排序、分页等功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..atoms, streamlit, pandas, typing
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Any, Callable, Optional, Tuple
from ..atoms.input import Input
from ..atoms.button import Button
from ..atoms.text import Text

class DataTable:
    """数据表格分子组件 - 提供统一的表格显示接口"""

    @staticmethod
    def render(
        data: pd.DataFrame,
        columns: List[str] = None,
        sortable: bool = True,
        filterable: bool = True,
        paginated: bool = True,
        page_size: int = 20,
        selection_mode: str = None,  # 'single', 'multiple', None
        on_row_select: Callable = None,
        key: str = "data_table",
        height: int = None,
        use_container_width: bool = True
    ) -> Dict[str, Any]:
        """
        渲染数据表格
        
        Args:
            data: 数据DataFrame
            columns: 显示的列
            sortable: 是否可排序
            filterable: 是否可筛选
            paginated: 是否分页
            page_size: 每页大小
            selection_mode: 选择模式
            on_row_select: 行选择回调
            key: 组件唯一标识
            height: 表格高度
            use_container_width: 是否使用容器宽度
            
        Returns:
            Dict[str, Any]: 包含选中行、筛选数据等信息
        """
        result = {
            'selected_rows': [],
            'filtered_data': data,
            'current_page': 1,
            'total_pages': 1
        }

        if data.empty:
            Text.info("暂无数据")
            return result

        # 列选择
        display_data = data[columns] if columns else data.copy()
        
        # 筛选功能
        if filterable:
            display_data = DataTable._apply_filters(display_data, key)
            result['filtered_data'] = display_data
        
        # 排序功能
        if sortable:
            display_data = DataTable._apply_sorting(display_data, key)
        
        # 分页功能
        if paginated and len(display_data) > page_size:
            display_data, current_page, total_pages = DataTable._apply_pagination(
                display_data, page_size, key
            )
            result['current_page'] = current_page
            result['total_pages'] = total_pages
        
        # 显示表格
        if selection_mode:
            selected_data = st.dataframe(
                display_data,
                use_container_width=use_container_width,
                height=height,
                key=f"{key}_dataframe",
                on_select="rerun",
                selection_mode=selection_mode
            )
            
            if hasattr(selected_data, 'selection') and selected_data.selection:
                result['selected_rows'] = selected_data.selection.rows
                
                # 触发回调
                if on_row_select and result['selected_rows']:
                    selected_indices = result['selected_rows']
                    selected_data_rows = display_data.iloc[selected_indices]
                    on_row_select(selected_data_rows)
        else:
            st.dataframe(
                display_data,
                use_container_width=use_container_width,
                height=height,
                key=f"{key}_dataframe"
            )
        
        return result

    @staticmethod
    def _apply_filters(data: pd.DataFrame, key: str) -> pd.DataFrame:
        """应用筛选"""
        with st.expander("表格筛选", expanded=False):
            col1, col2, col3 = st.columns([2, 2, 1])
            
            with col1:
                filter_column = Input.selectbox(
                    "筛选列",
                    options=[""] + list(data.columns),
                    key=f"{key}_filter_col"
                )
            
            with col2:
                filter_value = Input.text(
                    "筛选值",
                    placeholder="输入筛选条件",
                    key=f"{key}_filter_value"
                )
            
            with col3:
                clear_filter = Button.secondary(
                    "清除",
                    key=f"{key}_clear_filter"
                )
                
                if clear_filter:
                    st.session_state[f"{key}_filter_col"] = ""
                    st.session_state[f"{key}_filter_value"] = ""
                    st.rerun()
            
            # 应用筛选
            if filter_column and filter_value:
                try:
                    # 尝试不同的筛选方式
                    if data[filter_column].dtype in ['object', 'string']:
                        # 字符串列：包含筛选
                        mask = data[filter_column].astype(str).str.contains(
                            filter_value, case=False, na=False
                        )
                    else:
                        # 数值列：精确匹配或范围筛选
                        try:
                            numeric_value = float(filter_value)
                            mask = data[filter_column] == numeric_value
                        except ValueError:
                            # 如果不能转换为数值，则转为字符串筛选
                            mask = data[filter_column].astype(str).str.contains(
                                filter_value, case=False, na=False
                            )
                    
                    data = data[mask]
                except Exception as e:
                    Text.error(f"筛选错误: {e}")
        
        return data

    @staticmethod
    def _apply_sorting(data: pd.DataFrame, key: str) -> pd.DataFrame:
        """应用排序"""
        with st.expander("表格排序", expanded=False):
            col1, col2, col3 = st.columns([2, 2, 1])
            
            with col1:
                sort_column = Input.selectbox(
                    "排序列",
                    options=[""] + list(data.columns),
                    key=f"{key}_sort_col"
                )
            
            with col2:
                sort_order = Input.selectbox(
                    "排序方式",
                    options=["升序", "降序"],
                    key=f"{key}_sort_order"
                )
            
            with col3:
                clear_sort = Button.secondary(
                    "清除",
                    key=f"{key}_clear_sort"
                )
                
                if clear_sort:
                    st.session_state[f"{key}_sort_col"] = ""
                    st.rerun()
            
            # 应用排序
            if sort_column:
                ascending = sort_order == "升序"
                try:
                    data = data.sort_values(by=sort_column, ascending=ascending)
                except Exception as e:
                    Text.error(f"排序错误: {e}")
        
        return data

    @staticmethod
    def _apply_pagination(
        data: pd.DataFrame, 
        page_size: int, 
        key: str
    ) -> Tuple[pd.DataFrame, int, int]:
        """应用分页"""
        total_rows = len(data)
        total_pages = (total_rows - 1) // page_size + 1
        
        # 分页控件
        col1, col2, col3, col4, col5 = st.columns([1, 1, 2, 1, 1])
        
        with col1:
            if Button.secondary("⬅️", key=f"{key}_prev_page"):
                current_page = st.session_state.get(f"{key}_current_page", 1)
                if current_page > 1:
                    st.session_state[f"{key}_current_page"] = current_page - 1
                    st.rerun()
        
        with col2:
            current_page = st.session_state.get(f"{key}_current_page", 1)
            Text.write(f"第 {current_page} 页")
        
        with col3:
            new_page = Input.number(
                "跳转到页",
                value=current_page,
                min_value=1,
                max_value=total_pages,
                key=f"{key}_page_input",
                label_visibility="collapsed"
            )
            if new_page != current_page:
                st.session_state[f"{key}_current_page"] = new_page
                st.rerun()
        
        with col4:
            Text.write(f"共 {total_pages} 页")
        
        with col5:
            if Button.secondary("➡️", key=f"{key}_next_page"):
                current_page = st.session_state.get(f"{key}_current_page", 1)
                if current_page < total_pages:
                    st.session_state[f"{key}_current_page"] = current_page + 1
                    st.rerun()
        
        # 获取当前页数据
        current_page = st.session_state.get(f"{key}_current_page", 1)
        start_idx = (current_page - 1) * page_size
        end_idx = start_idx + page_size
        
        return data.iloc[start_idx:end_idx], current_page, total_pages

    @staticmethod
    def simple_table(
        data: pd.DataFrame,
        columns: List[str] = None,
        key: str = "simple_table"
    ) -> None:
        """简单表格显示"""
        display_data = data[columns] if columns else data
        st.dataframe(display_data, use_container_width=True, key=key)

    @staticmethod
    def editable_table(
        data: pd.DataFrame,
        key: str = "editable_table",
        on_change: Callable = None
    ) -> pd.DataFrame:
        """可编辑表格"""
        edited_data = st.data_editor(
            data,
            use_container_width=True,
            key=key,
            on_change=on_change
        )
        return edited_data

    @staticmethod
    def summary_table(
        data: pd.DataFrame,
        group_by: str,
        agg_functions: Dict[str, str],
        key: str = "summary_table"
    ) -> None:
        """汇总表格"""
        try:
            # 执行分组汇总
            summary_data = data.groupby(group_by).agg(agg_functions).reset_index()
            
            Text.subheader(f"按 {group_by} 汇总")
            st.dataframe(summary_data, use_container_width=True, key=key)
            
        except Exception as e:
            Text.error(f"汇总错误: {e}")

    @staticmethod
    def comparison_table(
        data1: pd.DataFrame,
        data2: pd.DataFrame,
        labels: Tuple[str, str] = ("数据1", "数据2"),
        key: str = "comparison_table"
    ) -> None:
        """对比表格"""
        tab1, tab2 = st.tabs(labels)
        
        with tab1:
            st.dataframe(data1, use_container_width=True, key=f"{key}_1")
        
        with tab2:
            st.dataframe(data2, use_container_width=True, key=f"{key}_2")

    @staticmethod
    def export_table(
        data: pd.DataFrame,
        filename: str = "data",
        formats: List[str] = ["csv", "excel"],
        key: str = "export_table"
    ) -> None:
        """导出表格"""
        Text.caption("导出数据:")
        cols = st.columns(len(formats))
        
        for i, fmt in enumerate(formats):
            with cols[i]:
                if fmt == "csv":
                    csv_data = data.to_csv(index=False)
                    st.download_button(
                        "下载 CSV",
                        csv_data,
                        f"{filename}.csv",
                        "text/csv",
                        key=f"{key}_csv"
                    )
                elif fmt == "excel":
                    # 注意：需要安装 openpyxl
                    try:
                        excel_buffer = data.to_excel(index=False)
                        st.download_button(
                            "下载 Excel",
                            excel_buffer,
                            f"{filename}.xlsx",
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            key=f"{key}_excel"
                        )
                    except Exception:
                        Text.caption("Excel导出需要安装 openpyxl")

    @staticmethod
    def searchable_table(
        data: pd.DataFrame,
        search_columns: List[str] = None,
        key: str = "searchable_table"
    ) -> pd.DataFrame:
        """可搜索表格"""
        # 搜索框
        search_term = Input.text(
            "搜索",
            placeholder="输入搜索关键词...",
            key=f"{key}_search"
        )
        
        # 应用搜索
        if search_term:
            search_cols = search_columns or data.select_dtypes(include=['object']).columns
            mask = pd.Series([False] * len(data))
            
            for col in search_cols:
                if col in data.columns:
                    mask |= data[col].astype(str).str.contains(
                        search_term, case=False, na=False
                    )
            
            filtered_data = data[mask]
        else:
            filtered_data = data
        
        # 显示结果
        Text.caption(f"显示 {len(filtered_data)} 条记录（共 {len(data)} 条）")
        st.dataframe(filtered_data, use_container_width=True, key=f"{key}_display")
        
        return filtered_data
