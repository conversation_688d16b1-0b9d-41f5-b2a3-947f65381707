#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
筛选面板分子组件

模块描述: 组合多个输入组件的筛选功能面板
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..atoms, streamlit, typing
"""

import streamlit as st
from typing import Dict, Any, Callable, List, Optional
from ..atoms.input import Input
from ..atoms.button import Button
from ..atoms.text import Text

class FilterPanel:
    """筛选面板分子组件 - 组合多个输入组件的筛选功能"""

    @staticmethod
    def render(
        filters: Dict[str, Dict[str, Any]],
        on_filter_change: Callable[[Dict[str, Any]], Any] = None,
        key_prefix: str = "filter",
        title: str = "筛选条件",
        expanded: bool = True,
        apply_button: bool = True,
        reset_button: bool = True,
        layout: str = "columns"  # columns, rows, grid
    ) -> Dict[str, Any]:
        """
        渲染筛选面板
        
        Args:
            filters: 筛选器配置字典
            on_filter_change: 筛选变化回调函数
            key_prefix: 键前缀
            title: 面板标题
            expanded: 是否展开
            apply_button: 是否显示应用按钮
            reset_button: 是否显示重置按钮
            layout: 布局方式
            
        Returns:
            Dict[str, Any]: 筛选值字典
        """
        filter_values = {}
        
        with st.expander(title, expanded=expanded):
            # 根据布局方式渲染筛选器
            if layout == "columns":
                filter_values = FilterPanel._render_columns_layout(
                    filters, key_prefix
                )
            elif layout == "rows":
                filter_values = FilterPanel._render_rows_layout(
                    filters, key_prefix
                )
            elif layout == "grid":
                filter_values = FilterPanel._render_grid_layout(
                    filters, key_prefix
                )
            
            # 按钮区域
            if apply_button or reset_button:
                st.markdown("---")
                button_cols = st.columns([1, 1, 3] if apply_button and reset_button else [1, 4])
                
                if apply_button:
                    with button_cols[0]:
                        apply_clicked = Button.primary(
                            "应用筛选",
                            key=f"{key_prefix}_apply",
                            use_container_width=True
                        )
                        if apply_clicked and on_filter_change:
                            on_filter_change(filter_values)
                
                if reset_button:
                    with button_cols[1]:
                        reset_clicked = Button.secondary(
                            "重置",
                            key=f"{key_prefix}_reset",
                            use_container_width=True
                        )
                        if reset_clicked:
                            FilterPanel._reset_filters(filters, key_prefix)
                            st.rerun()
        
        return filter_values

    @staticmethod
    def _render_columns_layout(
        filters: Dict[str, Dict[str, Any]],
        key_prefix: str
    ) -> Dict[str, Any]:
        """列布局渲染"""
        filter_values = {}
        
        # 计算列数
        num_filters = len(filters)
        if num_filters <= 3:
            cols = st.columns(num_filters)
        else:
            # 分多行显示
            cols_per_row = 3
            rows = (num_filters + cols_per_row - 1) // cols_per_row
            
            filter_items = list(filters.items())
            for row in range(rows):
                start_idx = row * cols_per_row
                end_idx = min(start_idx + cols_per_row, num_filters)
                row_filters = filter_items[start_idx:end_idx]
                
                cols = st.columns(len(row_filters))
                for i, (filter_name, filter_config) in enumerate(row_filters):
                    with cols[i]:
                        value = FilterPanel._render_single_filter(
                            filter_name, filter_config, key_prefix
                        )
                        filter_values[filter_name] = value
            
            return filter_values
        
        # 单行显示
        for i, (filter_name, filter_config) in enumerate(filters.items()):
            with cols[i]:
                value = FilterPanel._render_single_filter(
                    filter_name, filter_config, key_prefix
                )
                filter_values[filter_name] = value
        
        return filter_values

    @staticmethod
    def _render_rows_layout(
        filters: Dict[str, Dict[str, Any]],
        key_prefix: str
    ) -> Dict[str, Any]:
        """行布局渲染"""
        filter_values = {}
        
        for filter_name, filter_config in filters.items():
            value = FilterPanel._render_single_filter(
                filter_name, filter_config, key_prefix
            )
            filter_values[filter_name] = value
        
        return filter_values

    @staticmethod
    def _render_grid_layout(
        filters: Dict[str, Dict[str, Any]],
        key_prefix: str
    ) -> Dict[str, Any]:
        """网格布局渲染"""
        filter_values = {}
        
        # 2x2 网格布局
        filter_items = list(filters.items())
        for i in range(0, len(filter_items), 2):
            cols = st.columns(2)
            
            # 第一列
            if i < len(filter_items):
                filter_name, filter_config = filter_items[i]
                with cols[0]:
                    value = FilterPanel._render_single_filter(
                        filter_name, filter_config, key_prefix
                    )
                    filter_values[filter_name] = value
            
            # 第二列
            if i + 1 < len(filter_items):
                filter_name, filter_config = filter_items[i + 1]
                with cols[1]:
                    value = FilterPanel._render_single_filter(
                        filter_name, filter_config, key_prefix
                    )
                    filter_values[filter_name] = value
        
        return filter_values

    @staticmethod
    def _render_single_filter(
        filter_name: str,
        filter_config: Dict[str, Any],
        key_prefix: str
    ) -> Any:
        """渲染单个筛选器"""
        filter_type = filter_config.get('type', 'text')
        label = filter_config.get('label', filter_name)
        options = filter_config.get('options', [])
        default = filter_config.get('default')
        help_text = filter_config.get('help')
        disabled = filter_config.get('disabled', False)
        
        key = f"{key_prefix}_{filter_name}"
        
        if filter_type == 'selectbox':
            return Input.selectbox(
                label, options, 
                index=0 if default is None else (options.index(default) if default in options else 0),
                key=key, help=help_text, disabled=disabled
            )
        elif filter_type == 'multiselect':
            return Input.multiselect(
                label, options, default=default or [],
                key=key, help=help_text, disabled=disabled
            )
        elif filter_type == 'checkbox':
            return Input.checkbox(
                label, value=default or False,
                key=key, help=help_text, disabled=disabled
            )
        elif filter_type == 'radio':
            return Input.radio(
                label, options,
                index=0 if default is None else (options.index(default) if default in options else 0),
                key=key, help=help_text, disabled=disabled
            )
        elif filter_type == 'slider':
            min_val = filter_config.get('min_value', 0)
            max_val = filter_config.get('max_value', 100)
            step = filter_config.get('step', 1)
            return Input.slider(
                label, min_value=min_val, max_value=max_val,
                value=default or min_val, step=step,
                key=key, help=help_text, disabled=disabled
            )
        elif filter_type == 'number':
            min_val = filter_config.get('min_value')
            max_val = filter_config.get('max_value')
            step = filter_config.get('step', 1)
            return Input.number(
                label, value=default or 0,
                min_value=min_val, max_value=max_val, step=step,
                key=key, help=help_text, disabled=disabled
            )
        elif filter_type == 'date':
            return Input.date_input(
                label, value=default,
                key=key, help=help_text, disabled=disabled
            )
        else:  # text
            return Input.text(
                label, value=default or "",
                placeholder=filter_config.get('placeholder', ''),
                key=key, help=help_text, disabled=disabled
            )

    @staticmethod
    def _reset_filters(filters: Dict[str, Dict[str, Any]], key_prefix: str) -> None:
        """重置筛选器"""
        for filter_name in filters.keys():
            key = f"{key_prefix}_{filter_name}"
            if key in st.session_state:
                del st.session_state[key]

    @staticmethod
    def quick_filters(
        quick_filter_configs: List[Dict[str, Any]],
        key_prefix: str = "quick_filter"
    ) -> Dict[str, Any]:
        """
        快速筛选器
        
        Args:
            quick_filter_configs: 快速筛选配置列表
            key_prefix: 键前缀
            
        Returns:
            Dict[str, Any]: 选中的快速筛选值
        """
        selected_filters = {}
        
        Text.caption("快速筛选:")
        cols = st.columns(len(quick_filter_configs))
        
        for i, config in enumerate(quick_filter_configs):
            with cols[i]:
                label = config.get('label', f'筛选{i+1}')
                value = config.get('value', {})
                
                if Button.secondary(
                    label,
                    key=f"{key_prefix}_{i}",
                    use_container_width=True
                ):
                    selected_filters.update(value)
        
        return selected_filters

    @staticmethod
    def saved_filters(
        saved_filters_key: str = "saved_filters",
        current_filters: Dict[str, Any] = None,
        key_prefix: str = "saved_filter"
    ) -> Dict[str, Any]:
        """
        保存的筛选器
        
        Args:
            saved_filters_key: 保存筛选器的session key
            current_filters: 当前筛选值
            key_prefix: 键前缀
            
        Returns:
            Dict[str, Any]: 选中的保存筛选值
        """
        # 初始化保存的筛选器
        if saved_filters_key not in st.session_state:
            st.session_state[saved_filters_key] = {}
        
        selected_filters = {}
        
        with st.expander("保存的筛选器", expanded=False):
            # 保存当前筛选器
            if current_filters:
                col1, col2 = st.columns([3, 1])
                with col1:
                    filter_name = Input.text(
                        "筛选器名称",
                        placeholder="输入筛选器名称",
                        key=f"{key_prefix}_name"
                    )
                with col2:
                    if Button.primary(
                        "保存",
                        key=f"{key_prefix}_save",
                        disabled=not filter_name.strip()
                    ):
                        st.session_state[saved_filters_key][filter_name] = current_filters
                        st.success(f"筛选器 '{filter_name}' 已保存")
                        st.rerun()
            
            # 显示保存的筛选器
            saved_filters = st.session_state[saved_filters_key]
            if saved_filters:
                st.markdown("---")
                for name, filters in saved_filters.items():
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        if Button.secondary(
                            f"📁 {name}",
                            key=f"{key_prefix}_load_{name}",
                            use_container_width=True
                        ):
                            selected_filters = filters
                    
                    with col2:
                        if Button.danger(
                            "🗑️",
                            key=f"{key_prefix}_delete_{name}",
                            help=f"删除筛选器 '{name}'"
                        ):
                            del st.session_state[saved_filters_key][name]
                            st.rerun()
        
        return selected_filters
