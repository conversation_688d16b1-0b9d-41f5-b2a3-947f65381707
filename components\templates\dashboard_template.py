#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
仪表盘模板组件

模块描述: 标准化的仪表盘布局模板，集成头部、导航、内容区域
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..organisms, streamlit, typing
"""

import streamlit as st
import pandas as pd
from typing import Dict, Any, List, Optional, Callable
from ..organisms.dashboard_header import DashboardHeader
from ..organisms.navigation_sidebar import NavigationSidebar
from ..organisms.analysis_panel import AnalysisPanel
from ..molecules.metric_card import MetricCard

class DashboardTemplate:
    """仪表盘模板组件 - 标准化的仪表盘布局"""

    @staticmethod
    def render(
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame] = None,
        key: str = "dashboard_template"
    ) -> Dict[str, Any]:
        """
        渲染仪表盘模板
        
        Args:
            config: 仪表盘配置
            data: 数据字典
            key: 组件唯一标识
            
        Returns:
            Dict[str, Any]: 用户交互结果
        """
        data = data or {}
        result = {
            'current_page': '',
            'user_interactions': {},
            'navigation_result': {},
            'header_result': {}
        }

        # 页面配置
        st.set_page_config(
            page_title=config.get('title', '数据分析仪表盘'),
            page_icon=config.get('icon', '📊'),
            layout=config.get('layout', 'wide'),
            initial_sidebar_state=config.get('sidebar_state', 'expanded')
        )

        # 初始化session state
        if 'current_page' not in st.session_state:
            st.session_state.current_page = config.get('default_page', '概览')

        # 渲染导航侧边栏
        nav_config = config.get('navigation', NavigationSidebar.email_navigation())
        nav_result = NavigationSidebar.render(
            navigation_config=nav_config,
            current_page=st.session_state.current_page,
            show_search=config.get('nav_search', True),
            show_favorites=config.get('nav_favorites', True),
            key=f"{key}_nav"
        )
        result['navigation_result'] = nav_result

        # 更新当前页面
        if nav_result['selected_page'] and nav_result['selected_page'] != st.session_state.current_page:
            st.session_state.current_page = nav_result['selected_page']
            st.rerun()

        # 渲染仪表盘头部
        header_config = config.get('header', {})
        header_result = DashboardHeader.render(
            title=header_config.get('title', config.get('title', '数据分析仪表盘')),
            subtitle=header_config.get('subtitle'),
            user_info=header_config.get('user_info'),
            navigation_items=header_config.get('navigation_items'),
            show_search=header_config.get('show_search', True),
            show_notifications=header_config.get('show_notifications', True),
            show_settings=header_config.get('show_settings', True),
            key=f"{key}_header"
        )
        result['header_result'] = header_result

        # 渲染主内容区域
        current_page = st.session_state.current_page
        result['current_page'] = current_page
        
        content_result = DashboardTemplate._render_content(
            current_page, config, data, key
        )
        result['user_interactions'] = content_result

        return result

    @staticmethod
    def _render_content(
        current_page: str,
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染主内容区域"""
        pages_config = config.get('pages', {})
        page_config = pages_config.get(current_page, {})
        
        if not page_config:
            # 默认页面配置
            return DashboardTemplate._render_default_page(current_page, data, key)
        
        layout_type = page_config.get('layout', 'single')
        
        if layout_type == 'metrics_and_charts':
            return DashboardTemplate._render_metrics_charts_layout(
                page_config, data, key
            )
        elif layout_type == 'analysis_panel':
            return DashboardTemplate._render_analysis_layout(
                page_config, data, key
            )
        elif layout_type == 'two_columns':
            return DashboardTemplate._render_two_columns_layout(
                page_config, data, key
            )
        elif layout_type == 'tabs':
            return DashboardTemplate._render_tabs_layout(
                page_config, data, key
            )
        else:
            return DashboardTemplate._render_single_layout(
                page_config, data, key
            )

    @staticmethod
    def _render_default_page(
        page_name: str,
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染默认页面"""
        st.header(f"📄 {page_name}")
        
        if page_name in ['数据概览', '概览', '首页']:
            return DashboardTemplate._render_overview_page(data, key)
        elif '分析' in page_name:
            return DashboardTemplate._render_analysis_page(data, key)
        elif '报告' in page_name:
            return DashboardTemplate._render_report_page(data, key)
        else:
            st.info(f"页面 '{page_name}' 正在开发中...")
            return {}

    @staticmethod
    def _render_overview_page(
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染概览页面"""
        st.subheader("📊 系统概览")
        
        # 关键指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_data = sum(len(df) for df in data.values()) if data else 0
            MetricCard.simple_card("数据总量", f"{total_data:,}", icon="📊")
        
        with col2:
            data_sources = len(data) if data else 0
            MetricCard.simple_card("数据源", f"{data_sources}", icon="🗄️")
        
        with col3:
            MetricCard.simple_card("系统状态", "正常", icon="🟢")
        
        with col4:
            MetricCard.simple_card("在线用户", "5", icon="👥")
        
        # 快速操作
        st.subheader("🚀 快速操作")
        
        col1, col2, col3, col4 = st.columns(4)
        
        actions = {}
        
        with col1:
            if st.button("📧 邮件分析", use_container_width=True, key=f"{key}_email"):
                st.session_state.current_page = '邮件分析'
                actions['email_analysis'] = True
                st.rerun()
        
        with col2:
            if st.button("📊 数据分析", use_container_width=True, key=f"{key}_data"):
                st.session_state.current_page = '数据分析'
                actions['data_analysis'] = True
                st.rerun()
        
        with col3:
            if st.button("📋 生成报告", use_container_width=True, key=f"{key}_report"):
                st.session_state.current_page = '分析报告'
                actions['generate_report'] = True
                st.rerun()
        
        with col4:
            if st.button("⚙️ 系统设置", use_container_width=True, key=f"{key}_settings"):
                st.session_state.current_page = '系统设置'
                actions['settings'] = True
                st.rerun()
        
        return actions

    @staticmethod
    def _render_analysis_page(
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染分析页面"""
        # 使用分析面板组件
        if 'email_data' in data:
            return AnalysisPanel.email_analysis_panel(
                email_data=data['email_data'],
                key=f"{key}_analysis"
            )
        elif data:
            # 使用第一个可用的数据集
            first_data = next(iter(data.values()))
            return AnalysisPanel.render(
                data=first_data,
                title="数据分析",
                key=f"{key}_analysis"
            )
        else:
            st.warning("暂无数据可供分析")
            return {}

    @staticmethod
    def _render_report_page(
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染报告页面"""
        st.info("报告页面功能开发中...")
        return {}

    @staticmethod
    def _render_metrics_charts_layout(
        page_config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染指标和图表布局"""
        # 指标区域
        metrics_config = page_config.get('metrics', [])
        if metrics_config:
            st.subheader("📊 关键指标")
            cols = st.columns(len(metrics_config))
            
            for i, metric in enumerate(metrics_config):
                with cols[i]:
                    MetricCard.simple_card(
                        metric.get('title', f'指标{i+1}'),
                        metric.get('value', '0'),
                        icon=metric.get('icon', '📈')
                    )
        
        # 图表区域
        charts_config = page_config.get('charts', [])
        if charts_config:
            st.subheader("📈 数据可视化")
            
            for i, chart in enumerate(charts_config):
                chart_data = data.get(chart.get('data_source', ''))
                if chart_data is not None:
                    from ..molecules.chart_container import ChartContainer
                    ChartContainer.render(
                        data=chart_data,
                        chart_type=chart.get('type', 'line'),
                        title=chart.get('title', f'图表{i+1}'),
                        config=chart.get('config', {}),
                        key=f"{key}_chart_{i}"
                    )
        
        return {}

    @staticmethod
    def _render_analysis_layout(
        page_config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染分析布局"""
        analysis_config = page_config.get('analysis', {})
        data_source = analysis_config.get('data_source', '')
        
        if data_source in data:
            return AnalysisPanel.render(
                data=data[data_source],
                title=page_config.get('title', '数据分析'),
                config=analysis_config,
                key=f"{key}_analysis"
            )
        else:
            st.warning(f"数据源 '{data_source}' 不存在")
            return {}

    @staticmethod
    def _render_two_columns_layout(
        page_config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染两列布局"""
        col1, col2 = st.columns(page_config.get('column_ratio', [1, 1]))
        
        left_config = page_config.get('left_column', {})
        right_config = page_config.get('right_column', {})
        
        result = {}
        
        with col1:
            if left_config:
                result['left'] = DashboardTemplate._render_column_content(
                    left_config, data, f"{key}_left"
                )
        
        with col2:
            if right_config:
                result['right'] = DashboardTemplate._render_column_content(
                    right_config, data, f"{key}_right"
                )
        
        return result

    @staticmethod
    def _render_tabs_layout(
        page_config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染标签页布局"""
        tabs_config = page_config.get('tabs', [])
        
        if not tabs_config:
            st.warning("标签页配置为空")
            return {}
        
        tab_names = [tab.get('name', f'标签{i+1}') for i, tab in enumerate(tabs_config)]
        tabs = st.tabs(tab_names)
        
        result = {}
        
        for i, (tab, tab_config) in enumerate(zip(tabs, tabs_config)):
            with tab:
                result[f'tab_{i}'] = DashboardTemplate._render_column_content(
                    tab_config, data, f"{key}_tab_{i}"
                )
        
        return result

    @staticmethod
    def _render_single_layout(
        page_config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染单列布局"""
        return DashboardTemplate._render_column_content(page_config, data, key)

    @staticmethod
    def _render_column_content(
        content_config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染列内容"""
        content_type = content_config.get('type', 'text')
        
        if content_type == 'metrics':
            metrics = content_config.get('metrics', [])
            cols = st.columns(len(metrics)) if metrics else []
            
            for i, metric in enumerate(metrics):
                with cols[i]:
                    MetricCard.simple_card(
                        metric.get('title', f'指标{i+1}'),
                        metric.get('value', '0'),
                        icon=metric.get('icon', '📈')
                    )
        
        elif content_type == 'chart':
            data_source = content_config.get('data_source', '')
            if data_source in data:
                from ..molecules.chart_container import ChartContainer
                ChartContainer.render(
                    data=data[data_source],
                    chart_type=content_config.get('chart_type', 'line'),
                    title=content_config.get('title', '图表'),
                    config=content_config.get('config', {}),
                    key=f"{key}_chart"
                )
        
        elif content_type == 'table':
            data_source = content_config.get('data_source', '')
            if data_source in data:
                from ..molecules.data_table import DataTable
                DataTable.render(
                    data=data[data_source],
                    key=f"{key}_table"
                )
        
        elif content_type == 'analysis':
            data_source = content_config.get('data_source', '')
            if data_source in data:
                return AnalysisPanel.render(
                    data=data[data_source],
                    title=content_config.get('title', '分析'),
                    config=content_config.get('config', {}),
                    key=f"{key}_analysis"
                )
        
        elif content_type == 'text':
            text = content_config.get('text', '')
            if text:
                st.markdown(text)
        
        return {}

    @staticmethod
    def email_dashboard_config() -> Dict[str, Any]:
        """邮件分析仪表盘专用配置"""
        return {
            'title': '📧 邮件分析仪表盘',
            'icon': '📧',
            'layout': 'wide',
            'default_page': '数据概览',
            'header': {
                'title': '邮件分析系统',
                'subtitle': '智能邮件数据分析平台',
                'user_info': {
                    'name': '分析师',
                    'email': '<EMAIL>',
                    'department': '数据部'
                }
            },
            'navigation': NavigationSidebar.email_navigation(),
            'pages': {
                '数据概览': {
                    'layout': 'metrics_and_charts',
                    'metrics': [
                        {'title': '总邮件数', 'value': '1,234', 'icon': '📧'},
                        {'title': '已分析', 'value': '1,180', 'icon': '✅'},
                        {'title': '准确率', 'value': '95.6%', 'icon': '🎯'},
                        {'title': '异常检测', 'value': '12', 'icon': '⚠️'}
                    ],
                    'charts': [
                        {
                            'title': '邮件类型分布',
                            'type': 'pie',
                            'data_source': 'email_data',
                            'config': {'values': 'email_type', 'names': 'email_type'}
                        }
                    ]
                },
                '邮件分析': {
                    'layout': 'analysis_panel',
                    'analysis': {
                        'data_source': 'email_data',
                        'filters': {
                            'sender': {
                                'type': 'multiselect',
                                'label': '发件人'
                            },
                            'email_type': {
                                'type': 'selectbox',
                                'label': '邮件类型'
                            }
                        }
                    }
                }
            }
        }
