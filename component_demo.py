#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
组件演示应用

模块描述: 展示新创建的模块化组件功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import streamlit as st
import pandas as pd
import numpy as np
import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 导入组件
from components.atoms import Button, Input, Text, Icon, Loading
from components.molecules import SearchBox, FilterPanel, MetricCard, DataTable, ChartContainer

def main():
    """主应用"""
    st.set_page_config(
        page_title="模块化组件演示",
        page_icon="🧩",
        layout="wide"
    )
    
    Text.title("🧩 模块化组件演示")
    Text.markdown("展示新创建的原子组件和分子组件功能")
    
    # 侧边栏导航
    with st.sidebar:
        Text.header("组件导航")
        demo_type = Input.selectbox(
            "选择演示类型",
            ["原子组件", "分子组件", "综合演示"],
            key="demo_type"
        )
    
    if demo_type == "原子组件":
        demo_atoms()
    elif demo_type == "分子组件":
        demo_molecules()
    else:
        demo_comprehensive()

def demo_atoms():
    """演示原子组件"""
    Text.header("🔬 原子组件演示")
    
    # 按钮组件演示
    with st.expander("按钮组件", expanded=True):
        Text.subheader("按钮类型")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if Button.primary("主要按钮"):
                Text.success("点击了主要按钮！")
        
        with col2:
            if Button.secondary("次要按钮"):
                Text.info("点击了次要按钮！")
        
        with col3:
            if Button.success("成功按钮"):
                Text.success("点击了成功按钮！")
        
        with col4:
            if Button.warning("警告按钮"):
                Text.warning("点击了警告按钮！")
        
        Text.subheader("带图标的按钮")
        col1, col2 = st.columns(2)
        
        with col1:
            if Button.with_icon("保存", "💾"):
                Text.success("保存成功！")
        
        with col2:
            if Button.with_icon("删除", "🗑️", icon_position="right"):
                Text.error("删除操作！")
    
    # 输入组件演示
    with st.expander("输入组件", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            text_input = Input.text("文本输入", placeholder="请输入文本...")
            number_input = Input.number("数字输入", value=10, min_value=0, max_value=100)
            checkbox_input = Input.checkbox("复选框", value=True)
        
        with col2:
            select_input = Input.selectbox("选择框", ["选项1", "选项2", "选项3"])
            multi_input = Input.multiselect("多选框", ["A", "B", "C", "D"], default=["A"])
            slider_input = Input.slider("滑块", min_value=0, max_value=100, value=50)
        
        if text_input:
            Text.info(f"您输入的文本: {text_input}")
    
    # 文本组件演示
    with st.expander("文本组件", expanded=True):
        Text.subheader("文本样式")
        Text.markdown("这是 **粗体** 和 *斜体* 文本")
        Text.caption("这是说明文字")
        Text.code("print('Hello, World!')", language="python")
        
        Text.subheader("消息类型")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            Text.success("成功消息")
        with col2:
            Text.info("信息消息")
        with col3:
            Text.warning("警告消息")
        with col4:
            Text.error("错误消息")
    
    # 图标组件演示
    with st.expander("图标组件", expanded=True):
        Text.subheader("常用图标")
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            Icon.success()
            Text.caption("成功")
        
        with col2:
            Icon.error()
            Text.caption("错误")
        
        with col3:
            Icon.warning()
            Text.caption("警告")
        
        with col4:
            Icon.info()
            Text.caption("信息")
        
        with col5:
            Icon.loading()
            Text.caption("加载")
        
        Text.subheader("自定义图标")
        Icon.render("🚀", size="large", title="火箭")
        Icon.render("📊", size="xl", color="blue", title="图表")
    
    # 加载组件演示
    with st.expander("加载组件", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            if Button.primary("显示加载动画"):
                with Loading.spinner("正在处理..."):
                    import time
                    time.sleep(2)
                Text.success("处理完成！")
        
        with col2:
            if Button.secondary("显示进度条"):
                Loading.simulate_progress(10, 0.1, "步骤 {current}/{total}")

def demo_molecules():
    """演示分子组件"""
    Text.header("🧪 分子组件演示")
    
    # 搜索框组件演示
    with st.expander("搜索框组件", expanded=True):
        Text.subheader("基础搜索框")
        search_value, search_clicked, clear_clicked = SearchBox.render(
            placeholder="搜索产品、用户或订单...",
            key="basic_search"
        )
        
        if search_clicked:
            Text.success(f"搜索: {search_value}")
        if clear_clicked:
            Text.info("搜索已清除")
        
        Text.subheader("带建议的搜索框")
        suggestions = ["Python", "JavaScript", "Java", "C++", "Go", "Rust"]
        search_value2, search_clicked2 = SearchBox.search_with_suggestions(
            suggestions=suggestions,
            placeholder="搜索编程语言...",
            key="suggestion_search"
        )
    
    # 筛选面板组件演示
    with st.expander("筛选面板组件", expanded=True):
        filters_config = {
            "department": {
                "type": "selectbox",
                "label": "部门",
                "options": ["全部", "技术部", "销售部", "市场部"],
                "default": "全部"
            },
            "status": {
                "type": "multiselect",
                "label": "状态",
                "options": ["活跃", "暂停", "完成"],
                "default": ["活跃"]
            },
            "score": {
                "type": "slider",
                "label": "评分",
                "min_value": 0,
                "max_value": 100,
                "default": 50
            },
            "date_range": {
                "type": "date",
                "label": "日期",
                "help": "选择查询日期"
            }
        }
        
        filter_values = FilterPanel.render(
            filters=filters_config,
            title="高级筛选",
            key="demo_filter"
        )
        
        if filter_values:
            Text.json(filter_values)
    
    # 指标卡片组件演示
    with st.expander("指标卡片组件", expanded=True):
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            MetricCard.simple_card("总用户数", "1,234", icon="👥")
        
        with col2:
            MetricCard.comparison_card("月收入", "¥50,000", "¥45,000", icon="💰")
        
        with col3:
            MetricCard.progress_card("项目进度", 75, 100, unit="%", icon="📊")
        
        with col4:
            MetricCard.status_card("系统状态", "正常", "所有服务运行正常", icon="🟢")
        
        # 多指标卡片
        Text.subheader("多指标卡片")
        metrics = [
            {"name": "今日访问", "value": "2,345", "delta": "+12%"},
            {"name": "新用户", "value": "123", "delta": "+5%"},
            {"name": "转化率", "value": "3.2%", "delta": "-0.1%", "delta_color": "inverse"}
        ]
        MetricCard.multi_metric_card("网站统计", metrics, icon="📈")
    
    # 数据表格组件演示
    with st.expander("数据表格组件", expanded=True):
        # 创建示例数据
        sample_data = pd.DataFrame({
            "姓名": ["张三", "李四", "王五", "赵六", "钱七"],
            "部门": ["技术部", "销售部", "技术部", "市场部", "技术部"],
            "工资": [8000, 6000, 9000, 7000, 8500],
            "入职日期": pd.date_range("2020-01-01", periods=5, freq="M"),
            "状态": ["在职", "在职", "离职", "在职", "在职"]
        })
        
        Text.subheader("可筛选排序表格")
        result = DataTable.render(
            data=sample_data,
            sortable=True,
            filterable=True,
            paginated=True,
            page_size=3,
            key="demo_table"
        )
        
        if result['selected_rows']:
            Text.info(f"选中了 {len(result['selected_rows'])} 行")
    
    # 图表容器组件演示
    with st.expander("图表容器组件", expanded=True):
        # 创建示例数据
        chart_data = pd.DataFrame({
            "月份": ["1月", "2月", "3月", "4月", "5月", "6月"],
            "销售额": [100, 120, 140, 110, 160, 180],
            "利润": [20, 25, 30, 22, 35, 40],
            "类别": ["A", "B", "A", "B", "A", "B"]
        })
        
        col1, col2 = st.columns(2)
        
        with col1:
            ChartContainer.render(
                data=chart_data,
                chart_type="line",
                title="销售趋势",
                config={"x": "月份", "y": "销售额"},
                key="sales_chart"
            )
        
        with col2:
            ChartContainer.render(
                data=chart_data,
                chart_type="bar",
                title="利润分析",
                config={"x": "月份", "y": "利润", "color": "类别"},
                key="profit_chart"
            )

def demo_comprehensive():
    """综合演示"""
    Text.header("🎯 综合演示 - 数据分析仪表盘")
    
    # 创建模拟数据
    np.random.seed(42)
    data = pd.DataFrame({
        "日期": pd.date_range("2024-01-01", periods=100, freq="D"),
        "销售额": np.random.normal(1000, 200, 100).cumsum(),
        "用户数": np.random.poisson(50, 100).cumsum(),
        "转化率": np.random.normal(0.05, 0.01, 100),
        "地区": np.random.choice(["北京", "上海", "广州", "深圳"], 100),
        "产品": np.random.choice(["产品A", "产品B", "产品C"], 100)
    })
    
    # 顶部指标卡片
    Text.subheader("📊 关键指标")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_sales = data["销售额"].iloc[-1]
        MetricCard.simple_card("总销售额", f"¥{total_sales:,.0f}", icon="💰")
    
    with col2:
        total_users = data["用户数"].iloc[-1]
        MetricCard.simple_card("总用户数", f"{total_users:,}", icon="👥")
    
    with col3:
        avg_conversion = data["转化率"].mean()
        MetricCard.simple_card("平均转化率", f"{avg_conversion:.2%}", icon="📈")
    
    with col4:
        MetricCard.status_card("系统状态", "正常", icon="🟢")
    
    # 筛选控制
    Text.subheader("🔍 数据筛选")
    filters = {
        "地区": {
            "type": "multiselect",
            "label": "地区",
            "options": data["地区"].unique().tolist(),
            "default": data["地区"].unique().tolist()
        },
        "产品": {
            "type": "selectbox",
            "label": "产品",
            "options": ["全部"] + data["产品"].unique().tolist(),
            "default": "全部"
        },
        "日期范围": {
            "type": "slider",
            "label": "天数",
            "min_value": 1,
            "max_value": 100,
            "default": 30
        }
    }
    
    filter_values = FilterPanel.render(
        filters=filters,
        title="筛选条件",
        expanded=False,
        key="dashboard_filter"
    )
    
    # 应用筛选
    filtered_data = data.copy()
    if filter_values.get("地区"):
        filtered_data = filtered_data[filtered_data["地区"].isin(filter_values["地区"])]
    
    if filter_values.get("产品") and filter_values["产品"] != "全部":
        filtered_data = filtered_data[filtered_data["产品"] == filter_values["产品"]]
    
    if filter_values.get("日期范围"):
        days = filter_values["日期范围"]
        filtered_data = filtered_data.tail(days)
    
    # 图表展示
    Text.subheader("📈 数据可视化")
    
    col1, col2 = st.columns(2)
    
    with col1:
        ChartContainer.render(
            data=filtered_data,
            chart_type="line",
            title="销售额趋势",
            config={"x": "日期", "y": "销售额"},
            key="sales_trend"
        )
    
    with col2:
        ChartContainer.render(
            data=filtered_data,
            chart_type="bar",
            title="地区销售分布",
            config={"x": "地区", "y": "销售额"},
            key="region_sales"
        )
    
    # 数据表格
    Text.subheader("📋 详细数据")
    DataTable.render(
        data=filtered_data,
        columns=["日期", "销售额", "用户数", "转化率", "地区", "产品"],
        sortable=True,
        filterable=True,
        paginated=True,
        page_size=10,
        key="detail_table"
    )

if __name__ == "__main__":
    main()
