#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统检查工具
用于验证所有组件是否正确集成并遵循项目规范
"""

import os
import sys
import logging
import psycopg2
import importlib
import json
from datetime import datetime
from config import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s',
    handlers=[
        logging.FileHandler("system_check.log", mode='w'),
        logging.StreamHandler(sys.stdout),
    ],
)
logger = logging.getLogger(__name__)

class SystemChecker:
    """系统检查类"""

    def __init__(self, config_instance=None):
        """初始化系统检查器"""
        self.config = config_instance or config
        self.db_config = {
            'host': self.config.database.host,
            'port': self.config.database.port,
            'database': self.config.database.database,
            'user': self.config.database.user,
            'password': self.config.database.password
        }
        self.results = {
            "database": {"status": "未检查", "details": {}},
            "modules": {"status": "未检查", "details": {}},
            "integration": {"status": "未检查", "details": {}},
            "workflow": {"status": "未检查", "details": {}}
        }

    def check_database(self):
        """检查数据库结构"""
        try:
            logger.info("开始检查数据库结构...")

            # 连接数据库 - 修正参数名
            conn_params = self.db_config.copy()
            if 'dbname' in conn_params:
                conn_params['database'] = conn_params.pop('dbname')
            conn = psycopg2.connect(**conn_params)
            cursor = conn.cursor()

            # 检查staff_info表
            cursor.execute("SELECT EXISTS(SELECT FROM information_schema.tables WHERE table_name = 'staff_info')")
            staff_info_exists = cursor.fetchone()[0]

            # 检查email_types表
            cursor.execute("SELECT EXISTS(SELECT FROM information_schema.tables WHERE table_name = 'email_types')")
            email_types_exists = cursor.fetchone()[0]

            # 检查email_attachments表
            cursor.execute("SELECT EXISTS(SELECT FROM information_schema.tables WHERE table_name = 'email_attachments')")
            email_attachments_exists = cursor.fetchone()[0]

            # 检查email_attachments表的字段
            if email_attachments_exists:
                cursor.execute("""
                    SELECT column_name FROM information_schema.columns
                    WHERE table_name = 'email_attachments'
                    AND column_name IN ('email_type', 'email_type_confidence', 'ai_analyzed', 'ai_analysis_result')
                """)
                columns = [row[0] for row in cursor.fetchall()]

                # 检查是否有所有必要的字段
                required_columns = ['email_type', 'email_type_confidence', 'ai_analyzed', 'ai_analysis_result']
                missing_columns = [col for col in required_columns if col not in columns]
            else:
                missing_columns = []

            # 检查email_types表的数据
            if email_types_exists:
                cursor.execute("SELECT COUNT(*) FROM email_types")
                email_types_count = cursor.fetchone()[0]
            else:
                email_types_count = 0

            # 关闭连接
            cursor.close()
            conn.close()

            # 更新结果
            self.results["database"]["details"] = {
                "staff_info_exists": staff_info_exists,
                "email_types_exists": email_types_exists,
                "email_attachments_exists": email_attachments_exists,
                "missing_columns": missing_columns,
                "email_types_count": email_types_count
            }

            # 判断状态
            if staff_info_exists and email_types_exists and email_attachments_exists and not missing_columns:
                self.results["database"]["status"] = "通过"
            else:
                self.results["database"]["status"] = "失败"
                if not staff_info_exists:
                    logger.error("数据库检查失败: staff_info表不存在")
                if not email_types_exists:
                    logger.error("数据库检查失败: email_types表不存在")
                if not email_attachments_exists:
                    logger.error("数据库检查失败: email_attachments表不存在")
                if missing_columns:
                    logger.error(f"数据库检查失败: email_attachments表缺少字段: {missing_columns}")

            logger.info(f"数据库检查结果: {self.results['database']['status']}")
            return self.results["database"]["status"] == "通过"
        except Exception as e:
            logger.error(f"数据库检查出错: {e}")
            self.results["database"]["status"] = "错误"
            self.results["database"]["details"]["error"] = str(e)
            return False

    def check_modules(self):
        """检查模块是否存在并可导入"""
        try:
            logger.info("开始检查模块...")

            # 需要检查的模块
            modules = [
                "email_module.email_connection",
                "email_module.email_filter",
                "email_module.email_type_analyzer",
                "ai.adapter"
            ]

            # 检查每个模块
            module_results = {}
            for module_name in modules:
                try:
                    module = importlib.import_module(module_name)
                    module_results[module_name] = {"status": "通过", "details": "模块可导入"}
                except ImportError as e:
                    module_results[module_name] = {"status": "失败", "details": str(e)}
                    logger.error(f"模块检查失败: {module_name} - {e}")

            # 更新结果
            self.results["modules"]["details"] = module_results

            # 判断状态
            if all(result["status"] == "通过" for result in module_results.values()):
                self.results["modules"]["status"] = "通过"
            else:
                self.results["modules"]["status"] = "失败"

            logger.info(f"模块检查结果: {self.results['modules']['status']}")
            return self.results["modules"]["status"] == "通过"
        except Exception as e:
            logger.error(f"模块检查出错: {e}")
            self.results["modules"]["status"] = "错误"
            self.results["modules"]["details"]["error"] = str(e)
            return False

    def check_integration(self):
        """检查组件集成"""
        try:
            logger.info("开始检查组件集成...")

            # 检查EmailFilter是否正确集成EmailTypeAnalyzer
            try:
                from email_module.email_filter import EmailFilter
                filter = EmailFilter(analyze_email_type=True)
                has_analyzer = hasattr(filter, "email_type_analyzer")

                if has_analyzer:
                    integration_filter = {"status": "通过", "details": "EmailFilter正确集成EmailTypeAnalyzer"}
                else:
                    integration_filter = {"status": "失败", "details": "EmailFilter未集成EmailTypeAnalyzer"}
                    logger.error("组件集成检查失败: EmailFilter未集成EmailTypeAnalyzer")
            except Exception as e:
                integration_filter = {"status": "错误", "details": str(e)}
                logger.error(f"组件集成检查出错: EmailFilter - {e}")

            # 检查EmailTypeAnalyzer是否正确集成AIAdapter
            try:
                from email_module.email_type_analyzer import EmailTypeAnalyzer
                analyzer = EmailTypeAnalyzer()
                has_ai_adapter = hasattr(analyzer, "ai_adapter")

                if has_ai_adapter:
                    integration_analyzer = {"status": "通过", "details": "EmailTypeAnalyzer正确集成AIAdapter"}
                else:
                    integration_analyzer = {"status": "失败", "details": "EmailTypeAnalyzer未集成AIAdapter"}
                    logger.error("组件集成检查失败: EmailTypeAnalyzer未集成AIAdapter")
            except Exception as e:
                integration_analyzer = {"status": "错误", "details": str(e)}
                logger.error(f"组件集成检查出错: EmailTypeAnalyzer - {e}")

            # 更新结果
            self.results["integration"]["details"] = {
                "email_filter": integration_filter,
                "email_type_analyzer": integration_analyzer
            }

            # 判断状态
            if (integration_filter["status"] == "通过" and
                integration_analyzer["status"] == "通过"):
                self.results["integration"]["status"] = "通过"
            else:
                self.results["integration"]["status"] = "失败"

            logger.info(f"组件集成检查结果: {self.results['integration']['status']}")
            return self.results["integration"]["status"] == "通过"
        except Exception as e:
            logger.error(f"组件集成检查出错: {e}")
            self.results["integration"]["status"] = "错误"
            self.results["integration"]["details"]["error"] = str(e)
            return False

    def check_workflow(self):
        """检查工作流程"""
        try:
            logger.info("开始检查工作流程...")

            # 检查定时任务脚本
            scheduled_tasks_exists = os.path.exists("scheduled_tasks.py")

            # 检查批量处理脚本
            batch_analyze_exists = os.path.exists("batch_analyze_emails.py")

            # 检查批处理脚本
            bat_exists = os.path.exists("run_scheduled_tasks.bat")

            # 更新结果
            self.results["workflow"]["details"] = {
                "scheduled_tasks_exists": scheduled_tasks_exists,
                "batch_analyze_exists": batch_analyze_exists,
                "bat_exists": bat_exists
            }

            # 判断状态
            if scheduled_tasks_exists and batch_analyze_exists and bat_exists:
                self.results["workflow"]["status"] = "通过"
            else:
                self.results["workflow"]["status"] = "失败"
                if not scheduled_tasks_exists:
                    logger.error("工作流程检查失败: scheduled_tasks.py不存在")
                if not batch_analyze_exists:
                    logger.error("工作流程检查失败: batch_analyze_emails.py不存在")
                if not bat_exists:
                    logger.error("工作流程检查失败: run_scheduled_tasks.bat不存在")

            logger.info(f"工作流程检查结果: {self.results['workflow']['status']}")
            return self.results["workflow"]["status"] == "通过"
        except Exception as e:
            logger.error(f"工作流程检查出错: {e}")
            self.results["workflow"]["status"] = "错误"
            self.results["workflow"]["details"]["error"] = str(e)
            return False

    def check_all(self):
        """检查所有内容"""
        logger.info("开始全面系统检查...")

        # 检查数据库
        db_result = self.check_database()

        # 检查模块
        modules_result = self.check_modules()

        # 检查集成
        integration_result = self.check_integration()

        # 检查工作流程
        workflow_result = self.check_workflow()

        # 判断总体状态
        if db_result and modules_result and integration_result and workflow_result:
            overall_status = "通过"
        else:
            overall_status = "失败"

        logger.info(f"系统检查完成，总体状态: {overall_status}")

        # 返回结果
        return {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "overall_status": overall_status,
            "results": self.results
        }

    def generate_report(self, output_file="system_check_report.json"):
        """生成检查报告"""
        # 执行检查
        results = self.check_all()

        # 保存报告
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        logger.info(f"检查报告已保存到: {output_file}")

        # 打印摘要
        print("\n" + "=" * 50)
        print("系统检查摘要")
        print("=" * 50)
        print(f"总体状态: {results['overall_status']}")
        print(f"数据库: {self.results['database']['status']}")
        print(f"模块: {self.results['modules']['status']}")
        print(f"集成: {self.results['integration']['status']}")
        print(f"工作流程: {self.results['workflow']['status']}")
        print("=" * 50)

        # 如果有失败项，打印详细信息
        if results["overall_status"] == "失败":
            print("\n失败项详细信息:")

            if self.results["database"]["status"] != "通过":
                print("\n数据库问题:")
                for key, value in self.results["database"]["details"].items():
                    print(f"  - {key}: {value}")

            if self.results["modules"]["status"] != "通过":
                print("\n模块问题:")
                for module, result in self.results["modules"]["details"].items():
                    if result["status"] != "通过":
                        print(f"  - {module}: {result['details']}")

            if self.results["integration"]["status"] != "通过":
                print("\n集成问题:")
                for component, result in self.results["integration"]["details"].items():
                    if result["status"] != "通过":
                        print(f"  - {component}: {result['details']}")

            if self.results["workflow"]["status"] != "通过":
                print("\n工作流程问题:")
                for key, value in self.results["workflow"]["details"].items():
                    if not value:
                        print(f"  - {key}: 不存在")

        return results

def main():
    """主函数"""
    checker = SystemChecker()
    checker.generate_report()

if __name__ == "__main__":
    main()
