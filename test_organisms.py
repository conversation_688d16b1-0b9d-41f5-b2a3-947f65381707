#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
有机体组件测试脚本

模块描述: 测试新创建的有机体组件是否正常工作
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_organisms_import():
    """测试有机体组件导入"""
    try:
        from components.organisms import AnalysisPanel, DashboardHeader, NavigationSidebar, ReportViewer
        
        print("✅ 有机体组件导入成功")
        return True
    except Exception as e:
        print(f"❌ 有机体组件导入失败: {e}")
        return False

def test_analysis_panel():
    """测试分析面板组件"""
    try:
        from components.organisms.analysis_panel import AnalysisPanel
        
        # 检查是否有render方法
        assert hasattr(AnalysisPanel, 'render'), "AnalysisPanel缺少render方法"
        assert callable(getattr(AnalysisPanel, 'render')), "AnalysisPanel.render不是可调用的"
        
        # 检查邮件分析专用方法
        assert hasattr(AnalysisPanel, 'email_analysis_panel'), "AnalysisPanel缺少email_analysis_panel方法"
        
        print("✅ AnalysisPanel组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ AnalysisPanel组件功能测试失败: {e}")
        return False

def test_dashboard_header():
    """测试仪表盘头部组件"""
    try:
        from components.organisms.dashboard_header import DashboardHeader
        
        # 检查是否有render方法
        assert hasattr(DashboardHeader, 'render'), "DashboardHeader缺少render方法"
        assert callable(getattr(DashboardHeader, 'render')), "DashboardHeader.render不是可调用的"
        
        # 检查其他方法
        assert hasattr(DashboardHeader, 'breadcrumb_header'), "DashboardHeader缺少breadcrumb_header方法"
        assert hasattr(DashboardHeader, 'status_header'), "DashboardHeader缺少status_header方法"
        assert hasattr(DashboardHeader, 'action_header'), "DashboardHeader缺少action_header方法"
        
        print("✅ DashboardHeader组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ DashboardHeader组件功能测试失败: {e}")
        return False

def test_navigation_sidebar():
    """测试导航侧边栏组件"""
    try:
        from components.organisms.navigation_sidebar import NavigationSidebar
        
        # 检查是否有render方法
        assert hasattr(NavigationSidebar, 'render'), "NavigationSidebar缺少render方法"
        assert callable(getattr(NavigationSidebar, 'render')), "NavigationSidebar.render不是可调用的"
        
        # 检查邮件导航配置方法
        assert hasattr(NavigationSidebar, 'email_navigation'), "NavigationSidebar缺少email_navigation方法"
        
        # 检查其他方法
        assert hasattr(NavigationSidebar, 'compact_navigation'), "NavigationSidebar缺少compact_navigation方法"
        assert hasattr(NavigationSidebar, 'breadcrumb_navigation'), "NavigationSidebar缺少breadcrumb_navigation方法"
        
        print("✅ NavigationSidebar组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ NavigationSidebar组件功能测试失败: {e}")
        return False

def test_report_viewer():
    """测试报告查看器组件"""
    try:
        from components.organisms.report_viewer import ReportViewer
        
        # 检查是否有render方法
        assert hasattr(ReportViewer, 'render'), "ReportViewer缺少render方法"
        assert callable(getattr(ReportViewer, 'render')), "ReportViewer.render不是可调用的"
        
        # 检查邮件分析报告方法
        assert hasattr(ReportViewer, 'email_analysis_report'), "ReportViewer缺少email_analysis_report方法"
        
        print("✅ ReportViewer组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ ReportViewer组件功能测试失败: {e}")
        return False

def test_component_dependencies():
    """测试组件依赖关系"""
    try:
        # 测试有机体组件是否能正确导入分子组件
        from components.organisms.analysis_panel import AnalysisPanel
        from components.molecules.search_box import SearchBox
        from components.molecules.filter_panel import FilterPanel
        from components.molecules.metric_card import MetricCard
        from components.molecules.data_table import DataTable
        from components.molecules.chart_container import ChartContainer
        
        print("✅ 有机体组件依赖关系测试通过")
        return True
    except Exception as e:
        print(f"❌ 有机体组件依赖关系测试失败: {e}")
        return False

def test_navigation_config():
    """测试导航配置"""
    try:
        from components.organisms.navigation_sidebar import NavigationSidebar
        
        # 测试邮件导航配置
        nav_config = NavigationSidebar.email_navigation()
        
        # 验证配置结构
        assert isinstance(nav_config, list), "导航配置应该是列表"
        assert len(nav_config) > 0, "导航配置不能为空"
        
        for group in nav_config:
            assert 'name' in group, "导航分组必须有name字段"
            assert 'items' in group, "导航分组必须有items字段"
            assert isinstance(group['items'], list), "导航项目必须是列表"
        
        print("✅ 导航配置测试通过")
        return True
    except Exception as e:
        print(f"❌ 导航配置测试失败: {e}")
        return False

def test_component_integration():
    """测试组件集成"""
    try:
        # 测试所有有机体组件是否能正确集成
        from components.organisms import AnalysisPanel, DashboardHeader, NavigationSidebar, ReportViewer
        
        # 检查所有组件都有render方法
        components = [AnalysisPanel, DashboardHeader, NavigationSidebar, ReportViewer]
        
        for component in components:
            assert hasattr(component, 'render'), f"{component.__name__}缺少render方法"
            assert callable(getattr(component, 'render')), f"{component.__name__}.render不是可调用的"
        
        print("✅ 组件集成测试通过")
        return True
    except Exception as e:
        print(f"❌ 组件集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试有机体组件...")
    print("=" * 50)
    
    tests = [
        test_organisms_import,
        test_analysis_panel,
        test_dashboard_header,
        test_navigation_sidebar,
        test_report_viewer,
        test_component_dependencies,
        test_navigation_config,
        test_component_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！有机体组件创建成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查组件实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
