#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
搜索框分子组件

模块描述: 组合输入框和按钮的搜索功能组件
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..atoms, streamlit, typing
"""

import streamlit as st
from typing import Optional, Callable, Dict, Any, List, Tuple
from ..atoms.input import Input
from ..atoms.button import Button
from ..atoms.icon import Icon

class SearchBox:
    """搜索框分子组件 - 组合输入框和按钮的搜索功能"""

    @staticmethod
    def render(
        placeholder: str = "请输入搜索关键词...",
        value: str = "",
        search_button_text: str = "搜索",
        clear_button: bool = True,
        on_search: Callable[[str], Any] = None,
        on_clear: Callable = None,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        **kwargs
    ) -> Tuple[str, bool, bool]:
        """
        渲染搜索框
        
        Args:
            placeholder: 占位符文本
            value: 初始值
            search_button_text: 搜索按钮文本
            clear_button: 是否显示清除按钮
            on_search: 搜索回调函数
            on_clear: 清除回调函数
            key: 组件唯一标识
            help: 帮助文本
            disabled: 是否禁用
            **kwargs: 其他参数
            
        Returns:
            Tuple[str, bool, bool]: (搜索值, 是否点击搜索, 是否点击清除)
        """
        # 创建列布局
        if clear_button:
            col1, col2, col3 = st.columns([6, 1, 1])
        else:
            col1, col2 = st.columns([8, 1])
        
        # 搜索输入框
        with col1:
            search_value = Input.text(
                label="",
                value=value,
                placeholder=placeholder,
                key=f"{key}_input" if key else None,
                help=help,
                disabled=disabled,
                label_visibility="collapsed",
                **kwargs
            )
        
        # 搜索按钮
        with col2:
            search_clicked = Button.primary(
                search_button_text,
                key=f"{key}_search" if key else None,
                disabled=disabled or not search_value.strip(),
                use_container_width=True
            )
        
        # 清除按钮
        clear_clicked = False
        if clear_button:
            with col3:
                clear_clicked = Button.secondary(
                    "清除",
                    key=f"{key}_clear" if key else None,
                    disabled=disabled or not search_value.strip(),
                    use_container_width=True
                )
        
        # 处理回调
        if search_clicked and on_search:
            on_search(search_value)
        
        if clear_clicked and on_clear:
            on_clear()
        
        return search_value, search_clicked, clear_clicked

    @staticmethod
    def advanced_search(
        filters: Dict[str, Dict[str, Any]],
        placeholder: str = "请输入搜索关键词...",
        value: str = "",
        key: str = None,
        **kwargs
    ) -> Tuple[str, Dict[str, Any], bool]:
        """
        高级搜索框
        
        Args:
            filters: 筛选器配置
            placeholder: 占位符文本
            value: 初始值
            key: 组件唯一标识
            **kwargs: 其他参数
            
        Returns:
            Tuple[str, Dict[str, Any], bool]: (搜索值, 筛选值, 是否点击搜索)
        """
        # 基础搜索框
        search_value, search_clicked, _ = SearchBox.render(
            placeholder=placeholder,
            value=value,
            key=f"{key}_basic" if key else None,
            **kwargs
        )
        
        # 高级筛选
        with st.expander("高级筛选", expanded=False):
            filter_values = {}
            
            # 创建筛选器
            for filter_name, filter_config in filters.items():
                filter_type = filter_config.get('type', 'text')
                label = filter_config.get('label', filter_name)
                options = filter_config.get('options', [])
                default = filter_config.get('default')
                
                if filter_type == 'selectbox':
                    filter_values[filter_name] = Input.selectbox(
                        label,
                        options,
                        key=f"{key}_filter_{filter_name}" if key else None
                    )
                elif filter_type == 'multiselect':
                    filter_values[filter_name] = Input.multiselect(
                        label,
                        options,
                        default=default,
                        key=f"{key}_filter_{filter_name}" if key else None
                    )
                elif filter_type == 'date':
                    filter_values[filter_name] = Input.date_input(
                        label,
                        key=f"{key}_filter_{filter_name}" if key else None
                    )
                else:
                    filter_values[filter_name] = Input.text(
                        label,
                        value=default or "",
                        key=f"{key}_filter_{filter_name}" if key else None
                    )
        
        return search_value, filter_values, search_clicked

    @staticmethod
    def search_with_suggestions(
        suggestions: List[str],
        placeholder: str = "请输入搜索关键词...",
        value: str = "",
        max_suggestions: int = 5,
        key: str = None,
        **kwargs
    ) -> Tuple[str, bool]:
        """
        带建议的搜索框
        
        Args:
            suggestions: 建议列表
            placeholder: 占位符文本
            value: 初始值
            max_suggestions: 最大建议数
            key: 组件唯一标识
            **kwargs: 其他参数
            
        Returns:
            Tuple[str, bool]: (搜索值, 是否点击搜索)
        """
        # 搜索框
        search_value, search_clicked, _ = SearchBox.render(
            placeholder=placeholder,
            value=value,
            key=f"{key}_main" if key else None,
            **kwargs
        )
        
        # 显示建议
        if search_value and suggestions:
            filtered_suggestions = [
                s for s in suggestions 
                if search_value.lower() in s.lower()
            ][:max_suggestions]
            
            if filtered_suggestions:
                st.caption("搜索建议:")
                for suggestion in filtered_suggestions:
                    if st.button(
                        f"🔍 {suggestion}",
                        key=f"{key}_suggestion_{suggestion}" if key else None,
                        use_container_width=True
                    ):
                        # 更新搜索值
                        st.session_state[f"{key}_input"] = suggestion
                        st.rerun()
        
        return search_value, search_clicked

    @staticmethod
    def search_with_history(
        history_key: str = "search_history",
        max_history: int = 10,
        placeholder: str = "请输入搜索关键词...",
        value: str = "",
        key: str = None,
        **kwargs
    ) -> Tuple[str, bool]:
        """
        带历史记录的搜索框
        
        Args:
            history_key: 历史记录存储键
            max_history: 最大历史记录数
            placeholder: 占位符文本
            value: 初始值
            key: 组件唯一标识
            **kwargs: 其他参数
            
        Returns:
            Tuple[str, bool]: (搜索值, 是否点击搜索)
        """
        # 初始化历史记录
        if history_key not in st.session_state:
            st.session_state[history_key] = []
        
        # 搜索框
        search_value, search_clicked, _ = SearchBox.render(
            placeholder=placeholder,
            value=value,
            key=f"{key}_main" if key else None,
            **kwargs
        )
        
        # 保存搜索历史
        if search_clicked and search_value.strip():
            history = st.session_state[history_key]
            if search_value not in history:
                history.insert(0, search_value)
                st.session_state[history_key] = history[:max_history]
        
        # 显示搜索历史
        history = st.session_state[history_key]
        if history:
            with st.expander("搜索历史", expanded=False):
                for i, hist_item in enumerate(history):
                    col1, col2 = st.columns([8, 1])
                    
                    with col1:
                        if st.button(
                            f"🕐 {hist_item}",
                            key=f"{key}_history_{i}" if key else None,
                            use_container_width=True
                        ):
                            # 使用历史搜索
                            st.session_state[f"{key}_input"] = hist_item
                            st.rerun()
                    
                    with col2:
                        if st.button(
                            "❌",
                            key=f"{key}_remove_{i}" if key else None,
                            help="删除此历史记录"
                        ):
                            # 删除历史记录
                            st.session_state[history_key].pop(i)
                            st.rerun()
        
        return search_value, search_clicked

    @staticmethod
    def quick_search(
        quick_options: List[str],
        placeholder: str = "请输入搜索关键词...",
        value: str = "",
        key: str = None,
        **kwargs
    ) -> Tuple[str, bool, str]:
        """
        快速搜索框
        
        Args:
            quick_options: 快速选项列表
            placeholder: 占位符文本
            value: 初始值
            key: 组件唯一标识
            **kwargs: 其他参数
            
        Returns:
            Tuple[str, bool, str]: (搜索值, 是否点击搜索, 选中的快速选项)
        """
        # 搜索框
        search_value, search_clicked, _ = SearchBox.render(
            placeholder=placeholder,
            value=value,
            key=f"{key}_main" if key else None,
            **kwargs
        )
        
        # 快速选项
        selected_option = ""
        if quick_options:
            st.caption("快速搜索:")
            cols = st.columns(len(quick_options))
            
            for i, option in enumerate(quick_options):
                with cols[i]:
                    if st.button(
                        option,
                        key=f"{key}_quick_{i}" if key else None,
                        use_container_width=True
                    ):
                        selected_option = option
                        # 更新搜索值
                        st.session_state[f"{key}_input"] = option
                        st.rerun()
        
        return search_value, search_clicked, selected_option
