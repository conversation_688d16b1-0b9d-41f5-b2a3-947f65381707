#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模板组件模块包

模块描述: 由有机体组件组合而成的页面模板，提供完整的页面布局和功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..organisms, ..molecules, ..atoms, streamlit, typing
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 模板组件导入
from .dashboard_template import DashboardTemplate
from .analysis_template import AnalysisTemplate
from .report_template import ReportTemplate
from .config_driven_renderer import ConfigDrivenRenderer

__all__ = [
    # 仪表盘模板
    'DashboardTemplate',
    # 分析模板
    'AnalysisTemplate',
    # 报告模板
    'ReportTemplate',
    # 配置驱动渲染器
    'ConfigDrivenRenderer'
]
