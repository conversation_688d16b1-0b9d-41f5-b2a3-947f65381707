#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基础测试脚本
"""

def test_step_by_step():
    """逐步测试导入"""
    
    print("1. 测试基础Python功能...")
    from datetime import datetime
    from dataclasses import dataclass
    print("✅ 基础Python功能正常")
    
    print("2. 测试枚举导入...")
    from enum import Enum
    print("✅ 枚举导入正常")
    
    print("3. 测试typing导入...")
    from typing import List, Dict, Any, Optional
    print("✅ typing导入正常")
    
    print("4. 测试domain.entities中的枚举...")
    try:
        import sys
        import os
        sys.path.insert(0, os.getcwd())
        
        # 直接导入枚举
        from domain.entities import TaskComplexity, TaskCategory, TaskStatus, EmployeeLevel
        print("✅ 枚举导入成功")
        
        # 测试枚举使用
        complexity = TaskComplexity.MEDIUM
        category = TaskCategory.DEVELOPMENT
        status = TaskStatus.COMPLETED
        level = EmployeeLevel.INTERMEDIATE
        print("✅ 枚举使用正常")
        
    except Exception as e:
        print(f"❌ 枚举导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("5. 测试Employee类...")
    try:
        from domain.entities import Employee
        
        employee = Employee(
            email="<EMAIL>",
            name="测试员工",
            department="技术部",
            role="开发工程师"
        )
        print("✅ Employee类创建成功")
        print(f"   员工信息: {employee.name} - {employee.department}")
        
    except Exception as e:
        print(f"❌ Employee类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("6. 测试WorkItem类...")
    try:
        from domain.entities import WorkItem
        
        work_item = WorkItem(
            title="测试任务",
            description="这是一个测试任务",
            duration_hours=4.0,
            complexity=TaskComplexity.MEDIUM,
            category=TaskCategory.DEVELOPMENT,
            date=datetime.now(),
            employee_email="<EMAIL>"
        )
        print("✅ WorkItem类创建成功")
        print(f"   任务信息: {work_item.title} - {work_item.duration_hours}小时")
        
    except Exception as e:
        print(f"❌ WorkItem类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("7. 测试WeeklyReport类...")
    try:
        from domain.entities import WeeklyReport
        
        report = WeeklyReport(
            report_id="TEST-001",
            employee=employee,
            week="2024-W01",
            work_items=[work_item],
            summary={"total_tasks": 1},
            metrics={"productivity": 0.8},
            ai_version="1.0.0",
            raw_text="测试周报内容"
        )
        print("✅ WeeklyReport类创建成功")
        print(f"   周报信息: {report.report_id} - {report.week}")
        
    except Exception as e:
        print(f"❌ WeeklyReport类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("8. 测试AnalysisResult类...")
    try:
        from domain.entities import AnalysisResult
        
        result = AnalysisResult(
            result_id="RESULT-001",
            report_id="TEST-001",
            analysis_type="test_analysis",
            result_data={"score": 0.9},
            confidence_score=0.85,
            model_version="1.0.0",
            processing_time=1.5
        )
        print("✅ AnalysisResult类创建成功")
        print(f"   分析结果: {result.result_id} - 置信度: {result.confidence_score}")
        
    except Exception as e:
        print(f"❌ AnalysisResult类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 开始基础测试...")
    
    if test_step_by_step():
        print("\n🎉 所有基础测试通过！")
    else:
        print("\n❌ 基础测试失败")
