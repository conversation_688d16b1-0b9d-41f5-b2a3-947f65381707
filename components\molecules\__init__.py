#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分子组件模块包

模块描述: 由原子组件组合而成的复合组件，提供更复杂的UI功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..atoms, streamlit, typing
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 分子组件导入
from .search_box import SearchBox
from .filter_panel import FilterPanel
from .metric_card import MetricCard
from .data_table import DataTable
from .chart_container import ChartContainer

__all__ = [
    # 搜索组件
    'SearchBox',
    # 筛选组件
    'FilterPanel',
    # 指标卡片
    'MetricCard',
    # 数据表格
    'DataTable',
    # 图表容器
    'ChartContainer'
]
