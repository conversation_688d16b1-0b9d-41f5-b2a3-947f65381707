#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模板组件测试脚本

模块描述: 测试新创建的模板组件是否正常工作
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_templates_import():
    """测试模板组件导入"""
    try:
        from components.templates import DashboardTemplate, AnalysisTemplate, ReportTemplate, ConfigDrivenRenderer
        
        print("✅ 模板组件导入成功")
        return True
    except Exception as e:
        print(f"❌ 模板组件导入失败: {e}")
        return False

def test_dashboard_template():
    """测试仪表盘模板组件"""
    try:
        from components.templates.dashboard_template import DashboardTemplate
        
        # 检查是否有render方法
        assert hasattr(DashboardTemplate, 'render'), "DashboardTemplate缺少render方法"
        assert callable(getattr(DashboardTemplate, 'render')), "DashboardTemplate.render不是可调用的"
        
        # 检查邮件仪表盘配置方法
        assert hasattr(DashboardTemplate, 'email_dashboard_config'), "DashboardTemplate缺少email_dashboard_config方法"
        
        # 测试配置生成
        config = DashboardTemplate.email_dashboard_config()
        assert isinstance(config, dict), "配置应该是字典类型"
        assert 'title' in config, "配置应该包含title字段"
        assert 'pages' in config, "配置应该包含pages字段"
        
        print("✅ DashboardTemplate组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ DashboardTemplate组件功能测试失败: {e}")
        return False

def test_analysis_template():
    """测试分析模板组件"""
    try:
        from components.templates.analysis_template import AnalysisTemplate
        
        # 检查是否有render方法
        assert hasattr(AnalysisTemplate, 'render'), "AnalysisTemplate缺少render方法"
        assert callable(getattr(AnalysisTemplate, 'render')), "AnalysisTemplate.render不是可调用的"
        
        # 检查邮件分析模板配置方法
        assert hasattr(AnalysisTemplate, 'email_analysis_template_config'), "AnalysisTemplate缺少email_analysis_template_config方法"
        
        # 测试配置生成
        config = AnalysisTemplate.email_analysis_template_config()
        assert isinstance(config, dict), "配置应该是字典类型"
        assert 'title' in config, "配置应该包含title字段"
        
        print("✅ AnalysisTemplate组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ AnalysisTemplate组件功能测试失败: {e}")
        return False

def test_report_template():
    """测试报告模板组件"""
    try:
        from components.templates.report_template import ReportTemplate
        
        # 检查是否有render方法
        assert hasattr(ReportTemplate, 'render'), "ReportTemplate缺少render方法"
        assert callable(getattr(ReportTemplate, 'render')), "ReportTemplate.render不是可调用的"
        
        # 检查报告生成方法
        assert hasattr(ReportTemplate, 'generate_report_from_data'), "ReportTemplate缺少generate_report_from_data方法"
        
        print("✅ ReportTemplate组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ ReportTemplate组件功能测试失败: {e}")
        return False

def test_config_driven_renderer():
    """测试配置驱动渲染器"""
    try:
        from components.templates.config_driven_renderer import ConfigDrivenRenderer
        
        # 检查是否有render方法
        assert hasattr(ConfigDrivenRenderer, 'render_from_config'), "ConfigDrivenRenderer缺少render_from_config方法"
        assert callable(getattr(ConfigDrivenRenderer, 'render_from_config')), "ConfigDrivenRenderer.render_from_config不是可调用的"
        
        # 检查示例配置生成方法
        assert hasattr(ConfigDrivenRenderer, 'create_sample_config'), "ConfigDrivenRenderer缺少create_sample_config方法"
        
        # 测试示例配置生成
        config = ConfigDrivenRenderer.create_sample_config()
        assert isinstance(config, dict), "配置应该是字典类型"
        assert 'page' in config, "配置应该包含page字段"
        assert 'components' in config, "配置应该包含components字段"
        
        print("✅ ConfigDrivenRenderer组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ ConfigDrivenRenderer组件功能测试失败: {e}")
        return False

def test_template_dependencies():
    """测试模板组件依赖关系"""
    try:
        # 测试模板组件是否能正确导入有机体组件
        from components.templates.dashboard_template import DashboardTemplate
        from components.organisms.dashboard_header import DashboardHeader
        from components.organisms.navigation_sidebar import NavigationSidebar
        from components.organisms.analysis_panel import AnalysisPanel
        
        print("✅ 模板组件依赖关系测试通过")
        return True
    except Exception as e:
        print(f"❌ 模板组件依赖关系测试失败: {e}")
        return False

def test_config_structure():
    """测试配置结构"""
    try:
        from components.templates.dashboard_template import DashboardTemplate
        from components.templates.analysis_template import AnalysisTemplate
        from components.templates.config_driven_renderer import ConfigDrivenRenderer
        
        # 测试仪表盘配置结构
        dashboard_config = DashboardTemplate.email_dashboard_config()
        required_fields = ['title', 'header', 'navigation', 'pages']
        for field in required_fields:
            assert field in dashboard_config, f"仪表盘配置缺少{field}字段"
        
        # 测试分析模板配置结构
        analysis_config = AnalysisTemplate.email_analysis_template_config()
        required_fields = ['title', 'data_cleaning', 'analysis_config']
        for field in required_fields:
            assert field in analysis_config, f"分析模板配置缺少{field}字段"
        
        # 测试配置驱动渲染器配置结构
        renderer_config = ConfigDrivenRenderer.create_sample_config()
        required_fields = ['page', 'components']
        for field in required_fields:
            assert field in renderer_config, f"渲染器配置缺少{field}字段"
        
        print("✅ 配置结构测试通过")
        return True
    except Exception as e:
        print(f"❌ 配置结构测试失败: {e}")
        return False

def test_template_integration():
    """测试模板组件集成"""
    try:
        # 测试所有模板组件是否能正确集成
        from components.templates import DashboardTemplate, AnalysisTemplate, ReportTemplate, ConfigDrivenRenderer
        
        # 检查所有模板组件都有render方法
        templates = [
            (DashboardTemplate, 'render'),
            (AnalysisTemplate, 'render'),
            (ReportTemplate, 'render'),
            (ConfigDrivenRenderer, 'render_from_config')
        ]
        
        for template_class, method_name in templates:
            assert hasattr(template_class, method_name), f"{template_class.__name__}缺少{method_name}方法"
            assert callable(getattr(template_class, method_name)), f"{template_class.__name__}.{method_name}不是可调用的"
        
        print("✅ 模板组件集成测试通过")
        return True
    except Exception as e:
        print(f"❌ 模板组件集成测试失败: {e}")
        return False

def test_scipy_dependency():
    """测试scipy依赖"""
    try:
        from scipy import stats
        print("✅ scipy依赖可用")
        return True
    except ImportError:
        print("⚠️ scipy未安装，统计分析功能可能受限")
        return True  # 不强制要求scipy
    except Exception as e:
        print(f"❌ scipy依赖测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试模板组件...")
    print("=" * 50)
    
    tests = [
        test_templates_import,
        test_dashboard_template,
        test_analysis_template,
        test_report_template,
        test_config_driven_renderer,
        test_template_dependencies,
        test_config_structure,
        test_template_integration,
        test_scipy_dependency
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！模板组件创建成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查组件实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
