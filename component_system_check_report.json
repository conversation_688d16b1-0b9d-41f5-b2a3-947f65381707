{"timestamp": "2025-05-27 03:58:02", "overall_status": "通过", "summary": {"atoms": "通过", "molecules": "通过", "organisms": "通过", "integration": "通过", "demo": "通过"}, "details": {"atoms": {"status": "通过", "details": {"Button": {"status": "通过", "details": "组件可导入且有可用方法"}, "Input": {"status": "通过", "details": "组件可导入且有可用方法"}, "Text": {"status": "通过", "details": "组件可导入且有可用方法"}, "Icon": {"status": "通过", "details": "组件可导入且有可用方法"}, "Loading": {"status": "通过", "details": "组件可导入且有可用方法"}}}, "molecules": {"status": "通过", "details": {"SearchBox": {"status": "通过", "details": "组件可导入且有render方法"}, "FilterPanel": {"status": "通过", "details": "组件可导入且有render方法"}, "MetricCard": {"status": "通过", "details": "组件可导入且有render方法"}, "DataTable": {"status": "通过", "details": "组件可导入且有render方法"}, "ChartContainer": {"status": "通过", "details": "组件可导入且有render方法"}}}, "organisms": {"status": "通过", "details": {"AnalysisPanel": {"status": "通过", "details": "组件可导入且有render方法"}, "DashboardHeader": {"status": "通过", "details": "组件可导入且有render方法"}, "NavigationSidebar": {"status": "通过", "details": "组件可导入且有render方法"}, "ReportViewer": {"status": "通过", "details": "组件可导入且有render方法"}}}, "integration": {"status": "通过", "details": {"原子组件包导入": {"status": "通过", "details": "所有原子组件可从包导入"}, "分子组件包导入": {"status": "通过", "details": "所有分子组件可从包导入"}, "有机体组件包导入": {"status": "通过", "details": "所有有机体组件可从包导入"}, "组件依赖": {"status": "通过", "details": "分子组件可正确使用原子组件"}}}, "demo": {"status": "通过", "details": {"演示文件存在": {"status": "通过", "details": "component_demo.py 文件存在"}, "启动脚本存在": {"status": "通过", "details": "run_component_demo.bat 文件存在"}, "测试脚本存在": {"status": "通过", "details": "test_components.py 文件存在"}, "有机体演示文件存在": {"status": "通过", "details": "organism_demo.py 文件存在"}, "有机体启动脚本存在": {"status": "通过", "details": "run_organism_demo.bat 文件存在"}}}}}