#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文本原子组件

模块描述: 提供统一的文本显示接口，支持多种文本类型和样式
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: streamlit, typing, enum
"""

import streamlit as st
from typing import Optional, Dict, Any
from enum import Enum

class TextType(Enum):
    """文本类型枚举"""
    TITLE = "title"
    HEADER = "header"
    SUBHEADER = "subheader"
    MARKDOWN = "markdown"
    CODE = "code"
    CAPTION = "caption"
    METRIC = "metric"
    LATEX = "latex"
    JSON = "json"

class Text:
    """文本原子组件 - 提供统一的文本显示接口"""

    @staticmethod
    def title(text: str, anchor: str = None, help: str = None) -> None:
        """标题"""
        st.title(text, anchor=anchor, help=help)

    @staticmethod
    def header(text: str, anchor: str = None, help: str = None, divider: bool = False) -> None:
        """头部"""
        st.header(text, anchor=anchor, help=help, divider=divider)

    @staticmethod
    def subheader(text: str, anchor: str = None, help: str = None, divider: bool = False) -> None:
        """子头部"""
        st.subheader(text, anchor=anchor, help=help, divider=divider)

    @staticmethod
    def markdown(text: str, unsafe_allow_html: bool = False, help: str = None) -> None:
        """Markdown文本"""
        st.markdown(text, unsafe_allow_html=unsafe_allow_html, help=help)

    @staticmethod
    def code(code: str, language: str = None, line_numbers: bool = False) -> None:
        """代码块"""
        st.code(code, language=language, line_numbers=line_numbers)

    @staticmethod
    def caption(text: str, unsafe_allow_html: bool = False, help: str = None) -> None:
        """说明文字"""
        st.caption(text, unsafe_allow_html=unsafe_allow_html, help=help)

    @staticmethod
    def metric(
        label: str, 
        value: str, 
        delta: str = None, 
        delta_color: str = "normal",
        help: str = None,
        label_visibility: str = "visible"
    ) -> None:
        """指标显示"""
        st.metric(
            label, 
            value, 
            delta=delta, 
            delta_color=delta_color,
            help=help,
            label_visibility=label_visibility
        )

    @staticmethod
    def latex(body: str) -> None:
        """LaTeX公式"""
        st.latex(body)

    @staticmethod
    def json(body: Dict[str, Any], expanded: bool = True) -> None:
        """JSON显示"""
        st.json(body, expanded=expanded)

    @staticmethod
    def write(*args, unsafe_allow_html: bool = False, **kwargs) -> None:
        """通用写入"""
        st.write(*args, unsafe_allow_html=unsafe_allow_html, **kwargs)

    @staticmethod
    def success(body: str, icon: str = None) -> None:
        """成功消息"""
        st.success(body, icon=icon)

    @staticmethod
    def info(body: str, icon: str = None) -> None:
        """信息消息"""
        st.info(body, icon=icon)

    @staticmethod
    def warning(body: str, icon: str = None) -> None:
        """警告消息"""
        st.warning(body, icon=icon)

    @staticmethod
    def error(body: str, icon: str = None) -> None:
        """错误消息"""
        st.error(body, icon=icon)

    @staticmethod
    def exception(e: Exception) -> None:
        """异常显示"""
        st.exception(e)

    @staticmethod
    def balloons() -> None:
        """气球动画"""
        st.balloons()

    @staticmethod
    def snow() -> None:
        """雪花动画"""
        st.snow()

    @staticmethod
    def toast(body: str, icon: str = None) -> None:
        """Toast消息"""
        st.toast(body, icon=icon)

    @staticmethod
    def status(label: str, state: str = "running", expanded: bool = False):
        """状态显示"""
        return st.status(label, state=state, expanded=expanded)

    @staticmethod
    def progress(value: float, text: str = None) -> None:
        """进度条"""
        progress_bar = st.progress(value, text=text)
        return progress_bar

    @staticmethod
    def spinner(text: str = "In progress..."):
        """加载旋转器"""
        return st.spinner(text)

    @staticmethod
    def empty():
        """空占位符"""
        return st.empty()

    @staticmethod
    def container(border: bool = False):
        """容器"""
        return st.container(border=border)

    @staticmethod
    def columns(spec, gap: str = "small"):
        """列布局"""
        return st.columns(spec, gap=gap)

    @staticmethod
    def tabs(tab_list: list):
        """标签页"""
        return st.tabs(tab_list)

    @staticmethod
    def expander(label: str, expanded: bool = False):
        """可展开容器"""
        return st.expander(label, expanded=expanded)

    @staticmethod
    def popover(label: str, help: str = None, disabled: bool = False):
        """弹出框"""
        return st.popover(label, help=help, disabled=disabled)

    @staticmethod
    def echo(code_location: str = "above"):
        """代码回显"""
        return st.echo(code_location=code_location)

    @staticmethod
    def help(obj) -> None:
        """帮助信息"""
        st.help(obj)
