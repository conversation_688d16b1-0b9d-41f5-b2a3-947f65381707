#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
组件测试脚本

模块描述: 测试新创建的模块化组件是否正常工作
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_atoms_import():
    """测试原子组件导入"""
    try:
        from components.atoms import Button, ButtonType, ButtonSize
        from components.atoms import Input, InputType
        from components.atoms import Text, TextType
        from components.atoms import Icon, IconType
        from components.atoms import Loading, LoadingType
        
        print("✅ 原子组件导入成功")
        return True
    except Exception as e:
        print(f"❌ 原子组件导入失败: {e}")
        return False

def test_molecules_import():
    """测试分子组件导入"""
    try:
        from components.molecules import SearchBox, FilterPanel
        
        print("✅ 分子组件导入成功")
        return True
    except Exception as e:
        print(f"❌ 分子组件导入失败: {e}")
        return False

def test_button_functionality():
    """测试按钮功能"""
    try:
        from components.atoms.button import Button, ButtonType, ButtonSize
        
        # 测试按钮类型和大小枚举
        assert ButtonType.PRIMARY.value == "primary"
        assert ButtonSize.MEDIUM.value == "medium"
        
        print("✅ 按钮组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 按钮组件功能测试失败: {e}")
        return False

def test_input_functionality():
    """测试输入组件功能"""
    try:
        from components.atoms.input import Input, InputType
        
        # 测试输入类型枚举
        assert InputType.TEXT.value == "text"
        assert InputType.SELECTBOX.value == "selectbox"
        
        print("✅ 输入组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 输入组件功能测试失败: {e}")
        return False

def test_icon_functionality():
    """测试图标组件功能"""
    try:
        from components.atoms.icon import Icon, IconType
        
        # 测试图标映射
        icons = Icon.get_available_icons()
        assert "success" in icons
        assert "error" in icons
        assert icons["success"] == "✅"
        
        print("✅ 图标组件功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 图标组件功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试模块化组件...")
    print("=" * 50)
    
    tests = [
        test_atoms_import,
        test_molecules_import,
        test_button_functionality,
        test_input_functionality,
        test_icon_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！模块化组件创建成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查组件实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
