#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
领域模型模块包

模块描述: 领域驱动设计的核心模块，包含实体、值对象、领域服务等
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: .entities, .value_objects
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 实体导入
from .entities import (
    Employee, WorkItem, WeeklyReport, AnalysisResult,
    TaskComplexity, TaskCategory, TaskStatus, EmployeeLevel
)

# 值对象导入
from .value_objects import (
    ProcessingContext, VisualizationConfig, FilterCriteria,
    MetricThresholds, AnalysisParameters, TimeRange
)

# 仓储接口导入（可选，如果文件存在）
try:
    from .repositories import (
        IEmployeeRepository, IWorkItemRepository, IWeeklyReportRepository,
        IAnalysisResultRepository, ITagRepository, IAnomalyRepository,
        ITemplateRepository, RepositoryFactory
    )
    _repositories_available = True
except ImportError:
    _repositories_available = False

# 领域服务导入（可选，如果文件存在）
try:
    from .services import (
        EmployeeDomainService, WorkItemDomainService,
        WeeklyReportDomainService, AnalyticsDomainService
    )
    _services_available = True
except ImportError:
    _services_available = False

# 领域事件导入（可选，如果文件存在）
try:
    from .events import (
        DomainEvent, EmployeeCreatedEvent, EmployeeUpdatedEvent,
        WeeklyReportSubmittedEvent, WeeklyReportAnalyzedEvent,
        AnomalyDetectedEvent, WorkItemCreatedEvent,
        PerformanceThresholdExceededEvent, IEventHandler, EventBus,
        get_event_bus, publish_event, register_event_handler
    )
    _events_available = True
except ImportError:
    _events_available = False

# 动态构建__all__列表
__all__ = [
    # 实体
    'Employee',
    'WorkItem',
    'WeeklyReport',
    'AnalysisResult',
    # 枚举
    'TaskComplexity',
    'TaskCategory',
    'TaskStatus',
    'EmployeeLevel',
    # 值对象
    'ProcessingContext',
    'VisualizationConfig',
    'FilterCriteria',
    'MetricThresholds',
    'AnalysisParameters',
    'TimeRange'
]

# 添加可选的仓储接口
if _repositories_available:
    __all__.extend([
        'IEmployeeRepository',
        'IWorkItemRepository',
        'IWeeklyReportRepository',
        'IAnalysisResultRepository',
        'ITagRepository',
        'IAnomalyRepository',
        'ITemplateRepository',
        'RepositoryFactory'
    ])

# 添加可选的领域服务
if _services_available:
    __all__.extend([
        'EmployeeDomainService',
        'WorkItemDomainService',
        'WeeklyReportDomainService',
        'AnalyticsDomainService'
    ])

# 添加可选的领域事件
if _events_available:
    __all__.extend([
        'DomainEvent',
        'EmployeeCreatedEvent',
        'EmployeeUpdatedEvent',
        'WeeklyReportSubmittedEvent',
        'WeeklyReportAnalyzedEvent',
        'AnomalyDetectedEvent',
        'WorkItemCreatedEvent',
        'PerformanceThresholdExceededEvent',
        'IEventHandler',
        'EventBus',
        'get_event_bus',
        'publish_event',
        'register_event_handler'
    ])
