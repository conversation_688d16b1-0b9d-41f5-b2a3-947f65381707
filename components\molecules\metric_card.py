#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
指标卡片分子组件

模块描述: 组合文本和图标显示指标的卡片组件
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..atoms, streamlit, typing
"""

import streamlit as st
from typing import Optional, Dict, Any, List
from ..atoms.text import Text
from ..atoms.icon import Icon

class MetricCard:
    """指标卡片分子组件 - 组合文本和图标显示指标"""

    @staticmethod
    def render(
        title: str,
        value: str,
        delta: str = None,
        delta_color: str = "normal",
        icon: str = None,
        description: str = None,
        trend_data: List[float] = None,
        color: str = None,
        border: bool = True,
        key: str = None
    ) -> None:
        """
        渲染指标卡片
        
        Args:
            title: 指标标题
            value: 指标值
            delta: 变化值
            delta_color: 变化颜色
            icon: 图标
            description: 描述
            trend_data: 趋势数据
            color: 卡片颜色
            border: 是否显示边框
            key: 组件唯一标识
        """
        with st.container(border=border):
            # 创建布局
            if icon:
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    Text.metric(title, value, delta, delta_color)
                    if description:
                        Text.caption(description)
                
                with col2:
                    Icon.render(icon, size="large")
            else:
                Text.metric(title, value, delta, delta_color)
                if description:
                    Text.caption(description)
            
            # 显示趋势图
            if trend_data:
                st.line_chart(trend_data, height=100)

    @staticmethod
    def simple_card(
        title: str,
        value: str,
        icon: str = None,
        color: str = None,
        key: str = None
    ) -> None:
        """
        简单指标卡片
        
        Args:
            title: 标题
            value: 值
            icon: 图标
            color: 颜色
            key: 组件唯一标识
        """
        MetricCard.render(
            title=title,
            value=value,
            icon=icon,
            color=color,
            key=key
        )

    @staticmethod
    def comparison_card(
        title: str,
        current_value: str,
        previous_value: str,
        icon: str = None,
        key: str = None
    ) -> None:
        """
        对比指标卡片
        
        Args:
            title: 标题
            current_value: 当前值
            previous_value: 之前值
            icon: 图标
            key: 组件唯一标识
        """
        # 计算变化
        try:
            current = float(current_value.replace('%', '').replace(',', ''))
            previous = float(previous_value.replace('%', '').replace(',', ''))
            delta = current - previous
            delta_str = f"{delta:+.1f}"
            delta_color = "normal" if delta == 0 else ("inverse" if delta < 0 else "normal")
        except:
            delta_str = None
            delta_color = "normal"
        
        MetricCard.render(
            title=title,
            value=current_value,
            delta=delta_str,
            delta_color=delta_color,
            icon=icon,
            description=f"上期: {previous_value}",
            key=key
        )

    @staticmethod
    def status_card(
        title: str,
        status: str,
        description: str = None,
        icon: str = None,
        key: str = None
    ) -> None:
        """
        状态指标卡片
        
        Args:
            title: 标题
            status: 状态
            description: 描述
            icon: 图标
            key: 组件唯一标识
        """
        # 根据状态设置颜色和图标
        status_config = {
            "正常": {"color": "green", "icon": "✅"},
            "警告": {"color": "orange", "icon": "⚠️"},
            "错误": {"color": "red", "icon": "❌"},
            "未知": {"color": "gray", "icon": "❓"}
        }
        
        config = status_config.get(status, status_config["未知"])
        display_icon = icon or config["icon"]
        
        with st.container(border=True):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                Text.subheader(title)
                Text.markdown(f"**{status}**")
                if description:
                    Text.caption(description)
            
            with col2:
                Icon.render(display_icon, size="large", color=config["color"])

    @staticmethod
    def progress_card(
        title: str,
        current: float,
        target: float,
        unit: str = "",
        icon: str = None,
        key: str = None
    ) -> None:
        """
        进度指标卡片
        
        Args:
            title: 标题
            current: 当前值
            target: 目标值
            unit: 单位
            icon: 图标
            key: 组件唯一标识
        """
        progress = min(current / target, 1.0) if target > 0 else 0
        percentage = progress * 100
        
        with st.container(border=True):
            if icon:
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    Text.subheader(title)
                    Text.markdown(f"**{current:,.1f}{unit}** / {target:,.1f}{unit}")
                    st.progress(progress)
                    Text.caption(f"完成度: {percentage:.1f}%")
                
                with col2:
                    Icon.render(icon, size="large")
            else:
                Text.subheader(title)
                Text.markdown(f"**{current:,.1f}{unit}** / {target:,.1f}{unit}")
                st.progress(progress)
                Text.caption(f"完成度: {percentage:.1f}%")

    @staticmethod
    def multi_metric_card(
        title: str,
        metrics: List[Dict[str, Any]],
        icon: str = None,
        key: str = None
    ) -> None:
        """
        多指标卡片
        
        Args:
            title: 卡片标题
            metrics: 指标列表，每个指标包含 name, value, delta 等
            icon: 图标
            key: 组件唯一标识
        """
        with st.container(border=True):
            # 标题行
            if icon:
                col1, col2 = st.columns([3, 1])
                with col1:
                    Text.subheader(title)
                with col2:
                    Icon.render(icon, size="large")
            else:
                Text.subheader(title)
            
            # 指标行
            if len(metrics) <= 3:
                cols = st.columns(len(metrics))
                for i, metric in enumerate(metrics):
                    with cols[i]:
                        Text.metric(
                            metric.get('name', ''),
                            metric.get('value', ''),
                            metric.get('delta'),
                            metric.get('delta_color', 'normal')
                        )
            else:
                # 多行显示
                for i in range(0, len(metrics), 3):
                    row_metrics = metrics[i:i+3]
                    cols = st.columns(len(row_metrics))
                    for j, metric in enumerate(row_metrics):
                        with cols[j]:
                            Text.metric(
                                metric.get('name', ''),
                                metric.get('value', ''),
                                metric.get('delta'),
                                metric.get('delta_color', 'normal')
                            )

    @staticmethod
    def kpi_card(
        title: str,
        value: float,
        target: float,
        unit: str = "",
        format_str: str = ".1f",
        icon: str = None,
        key: str = None
    ) -> None:
        """
        KPI指标卡片
        
        Args:
            title: 标题
            value: 当前值
            target: 目标值
            unit: 单位
            format_str: 格式化字符串
            icon: 图标
            key: 组件唯一标识
        """
        # 计算完成率
        completion_rate = (value / target * 100) if target > 0 else 0
        
        # 确定状态
        if completion_rate >= 100:
            status_icon = "✅"
            status_color = "green"
        elif completion_rate >= 80:
            status_icon = "⚠️"
            status_color = "orange"
        else:
            status_icon = "❌"
            status_color = "red"
        
        with st.container(border=True):
            # 主要指标
            Text.metric(
                title,
                f"{value:{format_str}}{unit}",
                f"{completion_rate:.1f}%",
                "normal"
            )
            
            # 状态和目标
            col1, col2 = st.columns([3, 1])
            with col1:
                Text.caption(f"目标: {target:{format_str}}{unit}")
            with col2:
                Icon.render(status_icon, color=status_color)

    @staticmethod
    def trend_card(
        title: str,
        current_value: str,
        trend_direction: str,  # "up", "down", "stable"
        trend_percentage: float = None,
        icon: str = None,
        key: str = None
    ) -> None:
        """
        趋势指标卡片
        
        Args:
            title: 标题
            current_value: 当前值
            trend_direction: 趋势方向
            trend_percentage: 趋势百分比
            icon: 图标
            key: 组件唯一标识
        """
        # 趋势图标和颜色
        trend_config = {
            "up": {"icon": "📈", "color": "green"},
            "down": {"icon": "📉", "color": "red"},
            "stable": {"icon": "➡️", "color": "gray"}
        }
        
        config = trend_config.get(trend_direction, trend_config["stable"])
        
        delta_str = None
        if trend_percentage is not None:
            delta_str = f"{trend_percentage:+.1f}%"
        
        with st.container(border=True):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                Text.metric(title, current_value, delta_str)
                Text.caption(f"趋势: {trend_direction}")
            
            with col2:
                if icon:
                    Icon.render(icon, size="large")
                else:
                    Icon.render(config["icon"], size="large", color=config["color"])
