#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
输入原子组件

模块描述: 提供统一的输入接口，支持多种输入类型
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: streamlit, typing, enum, datetime
"""

import streamlit as st
from typing import Optional, List, Any, Union
from enum import Enum
from datetime import datetime, date

class InputType(Enum):
    """输入类型枚举"""
    TEXT = "text"
    TEXTAREA = "textarea"
    NUMBER = "number"
    PASSWORD = "password"
    EMAIL = "email"
    URL = "url"
    SELECTBOX = "selectbox"
    MULTISELECT = "multiselect"
    CHECKBOX = "checkbox"
    RADIO = "radio"
    SLIDER = "slider"
    DATE = "date"
    TIME = "time"
    DATETIME = "datetime"
    FILE = "file"

class Input:
    """输入框原子组件 - 提供统一的输入接口"""

    @staticmethod
    def text(
        label: str,
        value: str = "",
        placeholder: str = "",
        max_chars: int = None,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        **kwargs
    ) -> str:
        """文本输入框"""
        return st.text_input(
            label,
            value=value,
            placeholder=placeholder,
            max_chars=max_chars,
            key=key,
            help=help,
            disabled=disabled,
            **kwargs
        )

    @staticmethod
    def textarea(
        label: str,
        value: str = "",
        height: int = 200,
        max_chars: int = None,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        **kwargs
    ) -> str:
        """文本域"""
        return st.text_area(
            label,
            value=value,
            height=height,
            max_chars=max_chars,
            key=key,
            help=help,
            disabled=disabled,
            **kwargs
        )

    @staticmethod
    def number(
        label: str,
        value: Union[int, float] = 0,
        min_value: Union[int, float] = None,
        max_value: Union[int, float] = None,
        step: Union[int, float] = 1,
        format: str = None,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        **kwargs
    ) -> Union[int, float]:
        """数字输入框"""
        return st.number_input(
            label,
            value=value,
            min_value=min_value,
            max_value=max_value,
            step=step,
            format=format,
            key=key,
            help=help,
            disabled=disabled,
            **kwargs
        )

    @staticmethod
    def selectbox(
        label: str,
        options: List[Any],
        index: int = 0,
        format_func: callable = str,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        **kwargs
    ) -> Any:
        """选择框"""
        return st.selectbox(
            label,
            options,
            index=index,
            format_func=format_func,
            key=key,
            help=help,
            disabled=disabled,
            **kwargs
        )

    @staticmethod
    def multiselect(
        label: str,
        options: List[Any],
        default: List[Any] = None,
        format_func: callable = str,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        **kwargs
    ) -> List[Any]:
        """多选框"""
        return st.multiselect(
            label,
            options,
            default=default or [],
            format_func=format_func,
            key=key,
            help=help,
            disabled=disabled,
            **kwargs
        )

    @staticmethod
    def checkbox(
        label: str,
        value: bool = False,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        **kwargs
    ) -> bool:
        """复选框"""
        return st.checkbox(
            label,
            value=value,
            key=key,
            help=help,
            disabled=disabled,
            **kwargs
        )

    @staticmethod
    def radio(
        label: str,
        options: List[Any],
        index: int = 0,
        format_func: callable = str,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        horizontal: bool = False,
        **kwargs
    ) -> Any:
        """单选框"""
        return st.radio(
            label,
            options,
            index=index,
            format_func=format_func,
            key=key,
            help=help,
            disabled=disabled,
            horizontal=horizontal,
            **kwargs
        )

    @staticmethod
    def slider(
        label: str,
        min_value: Union[int, float] = 0,
        max_value: Union[int, float] = 100,
        value: Union[int, float] = None,
        step: Union[int, float] = 1,
        format: str = None,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        **kwargs
    ) -> Union[int, float]:
        """滑块"""
        if value is None:
            value = min_value
        return st.slider(
            label,
            min_value=min_value,
            max_value=max_value,
            value=value,
            step=step,
            format=format,
            key=key,
            help=help,
            disabled=disabled,
            **kwargs
        )

    @staticmethod
    def date_input(
        label: str,
        value: date = None,
        min_value: date = None,
        max_value: date = None,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        **kwargs
    ) -> date:
        """日期输入"""
        if value is None:
            value = date.today()
        return st.date_input(
            label,
            value=value,
            min_value=min_value,
            max_value=max_value,
            key=key,
            help=help,
            disabled=disabled,
            **kwargs
        )

    @staticmethod
    def file_uploader(
        label: str,
        type: List[str] = None,
        accept_multiple_files: bool = False,
        key: str = None,
        help: str = None,
        disabled: bool = False,
        **kwargs
    ):
        """文件上传"""
        return st.file_uploader(
            label,
            type=type,
            accept_multiple_files=accept_multiple_files,
            key=key,
            help=help,
            disabled=disabled,
            **kwargs
        )
