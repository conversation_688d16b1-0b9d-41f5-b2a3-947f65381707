#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
页面组件模块包

模块描述: 完整的页面组件，集成所有层级组件提供完整的应用页面
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..templates, ..organisms, ..molecules, ..atoms, streamlit, typing
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 页面组件导入
from .unified_dashboard import UnifiedDashboard
from .analysis_workbench import AnalysisWorkbench
from .insight_center import InsightCenter
from .system_integration import SystemIntegration

__all__ = [
    # 统一仪表盘
    'UnifiedDashboard',
    # 分析工作台
    'AnalysisWorkbench',
    # 洞察中心
    'InsightCenter',
    # 系统集成
    'SystemIntegration'
]
