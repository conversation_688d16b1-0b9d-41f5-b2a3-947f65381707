#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
服务层测试脚本
"""

import sys
import os
sys.path.insert(0, os.getcwd())

def test_cache_service():
    """测试缓存服务"""
    print("测试缓存服务...")
    try:
        from services.data.cache_service import CacheService
        from core.types import ComponentConfig
        
        config = ComponentConfig(name="cache_test", version="1.0.0")
        cache = CacheService(config)
        
        # 初始化
        assert cache.initialize() == True
        print("✅ 缓存服务初始化成功")
        
        # 测试基本操作
        assert cache.set("test_key", "test_value", 60) == True
        value = cache.get("test_key")
        assert value == "test_value"
        print("✅ 缓存基本操作成功")
        
        # 测试复杂数据
        complex_data = {"name": "测试", "numbers": [1, 2, 3]}
        assert cache.set("complex", complex_data, 60) == True
        retrieved = cache.get("complex")
        assert retrieved == complex_data
        print("✅ 缓存复杂数据成功")
        
        # 测试统计
        stats = cache.get_stats()
        assert 'hits' in stats
        print("✅ 缓存统计功能正常")
        
        return True
    except Exception as e:
        print(f"❌ 缓存服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_employee_repository():
    """测试员工仓储"""
    print("测试员工仓储...")
    try:
        from services.data.employee_repository import EmployeeRepository
        from domain.entities import Employee, EmployeeLevel
        from core.types import ComponentConfig
        
        config = ComponentConfig(name="employee_repo", version="1.0.0")
        repo = EmployeeRepository(config)
        
        # 初始化
        assert repo.initialize() == True
        print("✅ 员工仓储初始化成功")
        
        # 测试查询现有员工（测试数据）
        employees = repo.get_active_employees()
        assert len(employees) > 0
        print(f"✅ 查询到 {len(employees)} 个活跃员工")
        
        # 测试按部门查询
        tech_employees = repo.get_by_department("技术部")
        assert len(tech_employees) > 0
        print(f"✅ 技术部有 {len(tech_employees)} 个员工")
        
        # 测试创建新员工
        new_employee = Employee(
            email="<EMAIL>",
            name="新员工",
            department="测试部",
            role="测试工程师",
            level=EmployeeLevel.JUNIOR
        )
        
        created = repo.create(new_employee)
        assert created.email == "<EMAIL>"
        print("✅ 创建新员工成功")
        
        # 测试获取员工
        retrieved = repo.get_by_id("<EMAIL>")
        assert retrieved is not None
        assert retrieved.name == "新员工"
        print("✅ 获取员工成功")
        
        return True
    except Exception as e:
        print(f"❌ 员工仓储测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_report_repository():
    """测试周报仓储"""
    print("测试周报仓储...")
    try:
        from services.data.report_repository import ReportRepository
        from domain.entities import WeeklyReport, Employee, WorkItem, TaskComplexity, TaskCategory
        from core.types import ComponentConfig
        from datetime import datetime
        
        config = ComponentConfig(name="report_repo", version="1.0.0")
        repo = ReportRepository(config)
        
        # 初始化
        assert repo.initialize() == True
        print("✅ 周报仓储初始化成功")
        
        # 创建测试数据
        employee = Employee(
            email="<EMAIL>",
            name="报告员工",
            department="技术部",
            role="开发工程师"
        )
        
        work_item = WorkItem(
            title="开发功能",
            description="开发新功能",
            duration_hours=8.0,
            complexity=TaskComplexity.MEDIUM,
            category=TaskCategory.DEVELOPMENT,
            date=datetime.now(),
            employee_email="<EMAIL>"
        )
        
        report = WeeklyReport(
            report_id="TEST-REPORT-001",
            employee=employee,
            week="2024-W01",
            work_items=[work_item],
            summary={"total_tasks": 1, "total_hours": 8.0},
            metrics={"productivity": 0.8},
            ai_version="1.0.0",
            raw_text="本周完成了新功能的开发工作。"
        )
        
        # 测试创建周报
        created = repo.create(report)
        assert created.report_id == "TEST-REPORT-001"
        print("✅ 创建周报成功")
        
        # 测试获取周报
        retrieved = repo.get_by_id("TEST-REPORT-001")
        assert retrieved is not None
        assert retrieved.week == "2024-W01"
        assert len(retrieved.work_items) == 1
        print("✅ 获取周报成功")
        
        # 测试查询功能
        reports = repo.get_reports_by_employee("<EMAIL>")
        assert len(reports) >= 1
        print("✅ 按员工查询周报成功")
        
        return True
    except Exception as e:
        print(f"❌ 周报仓储测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_service():
    """测试数据服务"""
    print("测试数据服务...")
    try:
        from services.data.data_service import DataService
        from services.data.employee_repository import EmployeeRepository
        from services.data.report_repository import ReportRepository
        from services.data.cache_service import CacheService
        from domain.entities import Employee, EmployeeLevel
        from core.types import ComponentConfig
        
        # 创建组件
        cache_config = ComponentConfig(name="cache", version="1.0.0")
        cache_service = CacheService(cache_config)
        
        employee_config = ComponentConfig(name="employee_repo", version="1.0.0")
        employee_repo = EmployeeRepository(employee_config)
        
        report_config = ComponentConfig(name="report_repo", version="1.0.0")
        report_repo = ReportRepository(report_config)
        
        data_config = ComponentConfig(name="data_service", version="1.0.0")
        repositories = {
            'employee': employee_repo,
            'report': report_repo
        }
        data_service = DataService(data_config, repositories, cache_service)
        
        # 初始化
        assert cache_service.initialize() == True
        assert employee_repo.initialize() == True
        assert report_repo.initialize() == True
        assert data_service.initialize() == True
        print("✅ 数据服务初始化成功")
        
        # 测试员工操作
        employee = Employee(
            email="<EMAIL>",
            name="数据服务测试员工",
            department="测试部",
            role="测试工程师",
            level=EmployeeLevel.INTERMEDIATE
        )
        
        saved = data_service.save_employee(employee)
        assert saved.email == "<EMAIL>"
        print("✅ 保存员工成功")
        
        retrieved = data_service.get_employee("<EMAIL>")
        assert retrieved is not None
        assert retrieved.name == "数据服务测试员工"
        print("✅ 获取员工成功")
        
        # 测试部门查询
        dept_employees = data_service.get_employees_by_department("技术部")
        assert len(dept_employees) > 0
        print(f"✅ 查询部门员工成功，共 {len(dept_employees)} 人")
        
        # 测试统计
        stats = data_service.get_statistics()
        assert 'total_employees' in stats
        print("✅ 获取统计信息成功")
        
        return True
    except Exception as e:
        print(f"❌ 数据服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualization_service():
    """测试可视化服务"""
    print("测试可视化服务...")
    try:
        from services.visualization.plotly_visualizer import PlotlyVisualizer
        from services.visualization.visualization_service import VisualizationService
        from services.visualization.chart_factory import ChartFactory
        from core.types import ComponentConfig
        
        # 创建可视化器
        plotly_viz = PlotlyVisualizer()
        assert plotly_viz.get_name() == "plotly_visualizer"
        chart_types = plotly_viz.get_supported_chart_types()
        assert len(chart_types) > 0
        assert "line" in chart_types
        assert "bar" in chart_types
        print(f"✅ Plotly可视化器支持 {len(chart_types)} 种图表类型")
        
        # 创建可视化服务
        config = ComponentConfig(name="viz_service", version="1.0.0")
        viz_service = VisualizationService(config, [plotly_viz])
        
        assert viz_service.initialize() == True
        print("✅ 可视化服务初始化成功")
        
        # 测试图表工厂
        chart_factory = ChartFactory(plotly_viz)
        templates = chart_factory.get_available_templates()
        assert len(templates) > 0
        assert 'workload_trend' in templates
        print(f"✅ 图表工厂支持 {len(templates)} 种模板")
        
        # 测试服务信息
        info = viz_service.get_service_info()
        assert info['service_name'] == "viz_service"
        assert info['visualizer_count'] == 1
        print("✅ 可视化服务信息正确")
        
        return True
    except Exception as e:
        print(f"❌ 可视化服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_service_integration():
    """测试服务集成"""
    print("测试服务集成...")
    try:
        from services.data.data_service import DataService
        from services.data.employee_repository import EmployeeRepository
        from services.data.cache_service import CacheService
        from services.visualization.visualization_service import VisualizationService
        from services.visualization.plotly_visualizer import PlotlyVisualizer
        from core.types import ComponentConfig
        
        # 创建所有服务
        cache_config = ComponentConfig(name="cache", version="1.0.0")
        cache_service = CacheService(cache_config)
        
        employee_config = ComponentConfig(name="employee_repo", version="1.0.0")
        employee_repo = EmployeeRepository(employee_config)
        
        data_config = ComponentConfig(name="data_service", version="1.0.0")
        data_service = DataService(data_config, {'employee': employee_repo}, cache_service)
        
        viz_config = ComponentConfig(name="viz_service", version="1.0.0")
        plotly_viz = PlotlyVisualizer()
        viz_service = VisualizationService(viz_config, [plotly_viz], cache_service)
        
        # 初始化所有服务
        assert cache_service.initialize() == True
        assert employee_repo.initialize() == True
        assert data_service.initialize() == True
        assert viz_service.initialize() == True
        print("✅ 所有服务初始化成功")
        
        # 测试数据流
        employees = data_service.get_employees_by_department("技术部")
        assert len(employees) > 0
        print(f"✅ 获取到 {len(employees)} 个技术部员工")
        
        # 测试缓存集成
        test_data = {"employees": len(employees), "timestamp": "2024-01-27"}
        assert cache_service.set("integration_test", test_data, 300) == True
        cached_data = cache_service.get("integration_test")
        assert cached_data == test_data
        print("✅ 缓存集成测试成功")
        
        # 测试可视化服务信息
        viz_info = viz_service.get_service_info()
        assert viz_info['cache_enabled'] == True
        print("✅ 可视化服务缓存集成成功")
        
        return True
    except Exception as e:
        print(f"❌ 服务集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始服务层测试...")
    
    tests = [
        test_cache_service,
        test_employee_repository,
        test_report_repository,
        test_data_service,
        test_visualization_service,
        test_service_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n{'='*50}")
        if test():
            passed += 1
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有服务层测试通过！")
        print("\n📋 已完成的服务模块：")
        print("  ✅ services/data/cache_service - 缓存服务")
        print("  ✅ services/data/employee_repository - 员工仓储")
        print("  ✅ services/data/report_repository - 周报仓储")
        print("  ✅ services/data/data_service - 数据服务")
        print("  ✅ services/visualization - 可视化服务")
        print("  ✅ 服务间集成测试")
    else:
        print("⚠️ 部分测试失败")
