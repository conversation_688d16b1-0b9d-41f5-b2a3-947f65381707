#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
报告查看器有机体组件

模块描述: 报告展示和交互功能，支持多种报告格式和导出功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..molecules, ..atoms, streamlit, pandas, typing
"""

import streamlit as st
import pandas as pd
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from ..atoms.text import Text
from ..atoms.button import Button
from ..atoms.icon import Icon
from ..atoms.input import Input
from ..molecules.metric_card import MetricCard
from ..molecules.chart_container import ChartContainer
from ..molecules.data_table import DataTable

class ReportViewer:
    """报告查看器有机体组件 - 报告展示和交互功能"""

    @staticmethod
    def render(
        report_data: Dict[str, Any],
        report_type: str = "analysis",
        interactive: bool = True,
        export_options: bool = True,
        key: str = "report_viewer"
    ) -> Dict[str, Any]:
        """
        渲染报告查看器
        
        Args:
            report_data: 报告数据
            report_type: 报告类型
            interactive: 是否支持交互
            export_options: 是否显示导出选项
            key: 组件唯一标识
            
        Returns:
            Dict[str, Any]: 用户交互结果
        """
        result = {
            'exported': False,
            'shared': False,
            'bookmarked': False,
            'comments_added': False
        }

        # 报告头部
        ReportViewer._render_report_header(
            report_data, interactive, export_options, key, result
        )
        
        # 报告内容
        if report_type == "analysis":
            ReportViewer._render_analysis_report(report_data, key)
        elif report_type == "summary":
            ReportViewer._render_summary_report(report_data, key)
        elif report_type == "detailed":
            ReportViewer._render_detailed_report(report_data, key)
        else:
            ReportViewer._render_custom_report(report_data, key)
        
        # 报告底部
        if interactive:
            ReportViewer._render_report_footer(key, result)
        
        return result

    @staticmethod
    def _render_report_header(
        report_data: Dict[str, Any],
        interactive: bool,
        export_options: bool,
        key: str,
        result: Dict[str, Any]
    ) -> None:
        """渲染报告头部"""
        # 标题行
        col1, col2, col3 = st.columns([4, 2, 2])
        
        with col1:
            title = report_data.get('title', '分析报告')
            Text.title(title)
            
            subtitle = report_data.get('subtitle', '')
            if subtitle:
                Text.caption(subtitle)
        
        with col2:
            # 报告信息
            created_date = report_data.get('created_date', datetime.now())
            if isinstance(created_date, str):
                created_date = datetime.fromisoformat(created_date)
            
            Text.caption(f"📅 生成时间: {created_date.strftime('%Y-%m-%d %H:%M')}")
            Text.caption(f"👤 生成者: {report_data.get('author', '系统')}")
            Text.caption(f"📊 报告类型: {report_data.get('type', '分析报告')}")
        
        with col3:
            # 操作按钮
            if export_options:
                if Button.primary("📤 导出", key=f"{key}_export"):
                    ReportViewer._show_export_options(report_data, key)
                    result['exported'] = True
            
            if interactive:
                col3_1, col3_2 = st.columns(2)
                
                with col3_1:
                    if Button.secondary("🔖", key=f"{key}_bookmark"):
                        Text.success("已添加书签")
                        result['bookmarked'] = True
                
                with col3_2:
                    if Button.secondary("📤", key=f"{key}_share"):
                        Text.info("分享功能开发中...")
                        result['shared'] = True
        
        st.markdown("---")

    @staticmethod
    def _render_analysis_report(report_data: Dict[str, Any], key: str) -> None:
        """渲染分析报告"""
        # 执行摘要
        summary = report_data.get('summary', {})
        if summary:
            Text.subheader("📋 执行摘要")
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                MetricCard.simple_card(
                    "数据总量",
                    str(summary.get('total_records', 0)),
                    icon="📊"
                )
            
            with col2:
                MetricCard.simple_card(
                    "分析完成率",
                    f"{summary.get('completion_rate', 0):.1f}%",
                    icon="✅"
                )
            
            with col3:
                MetricCard.simple_card(
                    "异常检测",
                    str(summary.get('anomalies', 0)),
                    icon="⚠️"
                )
            
            with col4:
                MetricCard.simple_card(
                    "置信度",
                    f"{summary.get('confidence', 0):.1f}%",
                    icon="🎯"
                )
        
        # 关键发现
        findings = report_data.get('findings', [])
        if findings:
            Text.subheader("🔍 关键发现")
            
            for i, finding in enumerate(findings):
                with st.container(border=True):
                    col1, col2 = st.columns([1, 10])
                    
                    with col1:
                        importance = finding.get('importance', 'medium')
                        if importance == 'high':
                            Icon.render("🔴", size="large")
                        elif importance == 'medium':
                            Icon.render("🟡", size="large")
                        else:
                            Icon.render("🟢", size="large")
                    
                    with col2:
                        Text.markdown(f"**{finding.get('title', f'发现 {i+1}')}**")
                        Text.markdown(finding.get('description', ''))
                        
                        if finding.get('recommendation'):
                            Text.caption(f"💡 建议: {finding['recommendation']}")
        
        # 数据可视化
        charts = report_data.get('charts', [])
        if charts:
            Text.subheader("📈 数据可视化")
            
            for i, chart in enumerate(charts):
                ChartContainer.render(
                    data=pd.DataFrame(chart.get('data', {})),
                    chart_type=chart.get('type', 'line'),
                    title=chart.get('title', f'图表 {i+1}'),
                    config=chart.get('config', {}),
                    key=f"{key}_chart_{i}"
                )
        
        # 详细数据
        detailed_data = report_data.get('detailed_data')
        if detailed_data is not None:
            Text.subheader("📋 详细数据")
            
            if isinstance(detailed_data, dict):
                detailed_data = pd.DataFrame(detailed_data)
            
            DataTable.render(
                data=detailed_data,
                sortable=True,
                filterable=True,
                paginated=True,
                key=f"{key}_detailed_table"
            )

    @staticmethod
    def _render_summary_report(report_data: Dict[str, Any], key: str) -> None:
        """渲染摘要报告"""
        # 概览指标
        metrics = report_data.get('metrics', {})
        if metrics:
            Text.subheader("📊 概览指标")
            
            # 动态创建指标卡片
            metric_items = list(metrics.items())
            cols = st.columns(min(len(metric_items), 4))
            
            for i, (metric_name, metric_value) in enumerate(metric_items):
                with cols[i % 4]:
                    MetricCard.simple_card(
                        metric_name,
                        str(metric_value),
                        icon="📈"
                    )
        
        # 趋势分析
        trends = report_data.get('trends', [])
        if trends:
            Text.subheader("📈 趋势分析")
            
            for trend in trends:
                with st.container(border=True):
                    Text.markdown(f"**{trend.get('name', '趋势')}**")
                    
                    direction = trend.get('direction', 'stable')
                    change = trend.get('change', 0)
                    
                    if direction == 'up':
                        Text.success(f"📈 上升 {change}%")
                    elif direction == 'down':
                        Text.error(f"📉 下降 {change}%")
                    else:
                        Text.info(f"➡️ 稳定 {change}%")
                    
                    Text.caption(trend.get('description', ''))
        
        # 建议事项
        recommendations = report_data.get('recommendations', [])
        if recommendations:
            Text.subheader("💡 建议事项")
            
            for i, rec in enumerate(recommendations):
                Text.markdown(f"{i+1}. {rec}")

    @staticmethod
    def _render_detailed_report(report_data: Dict[str, Any], key: str) -> None:
        """渲染详细报告"""
        # 分节显示
        sections = report_data.get('sections', [])
        
        for i, section in enumerate(sections):
            section_title = section.get('title', f'第{i+1}节')
            Text.subheader(section_title)
            
            # 文本内容
            content = section.get('content', '')
            if content:
                Text.markdown(content)
            
            # 数据表格
            table_data = section.get('table_data')
            if table_data is not None:
                if isinstance(table_data, dict):
                    table_data = pd.DataFrame(table_data)
                
                DataTable.render(
                    data=table_data,
                    key=f"{key}_section_{i}_table"
                )
            
            # 图表
            chart_data = section.get('chart_data')
            if chart_data:
                ChartContainer.render(
                    data=pd.DataFrame(chart_data.get('data', {})),
                    chart_type=chart_data.get('type', 'line'),
                    title=chart_data.get('title', ''),
                    config=chart_data.get('config', {}),
                    key=f"{key}_section_{i}_chart"
                )
            
            st.markdown("---")

    @staticmethod
    def _render_custom_report(report_data: Dict[str, Any], key: str) -> None:
        """渲染自定义报告"""
        # 自定义布局
        layout = report_data.get('layout', 'single_column')
        
        if layout == 'two_columns':
            col1, col2 = st.columns(2)
            
            with col1:
                ReportViewer._render_report_content(
                    report_data.get('left_content', {}), f"{key}_left"
                )
            
            with col2:
                ReportViewer._render_report_content(
                    report_data.get('right_content', {}), f"{key}_right"
                )
        
        elif layout == 'tabs':
            tab_data = report_data.get('tabs', [])
            if tab_data:
                tab_names = [tab.get('name', f'标签{i+1}') for i, tab in enumerate(tab_data)]
                tabs = st.tabs(tab_names)
                
                for i, (tab, tab_content) in enumerate(zip(tabs, tab_data)):
                    with tab:
                        ReportViewer._render_report_content(
                            tab_content.get('content', {}), f"{key}_tab_{i}"
                        )
        
        else:
            # 单列布局
            ReportViewer._render_report_content(
                report_data.get('content', {}), key
            )

    @staticmethod
    def _render_report_content(content: Dict[str, Any], key: str) -> None:
        """渲染报告内容"""
        content_type = content.get('type', 'text')
        
        if content_type == 'text':
            Text.markdown(content.get('text', ''))
        
        elif content_type == 'metrics':
            metrics = content.get('metrics', {})
            cols = st.columns(len(metrics))
            
            for i, (name, value) in enumerate(metrics.items()):
                with cols[i]:
                    MetricCard.simple_card(name, str(value))
        
        elif content_type == 'chart':
            ChartContainer.render(
                data=pd.DataFrame(content.get('data', {})),
                chart_type=content.get('chart_type', 'line'),
                title=content.get('title', ''),
                key=f"{key}_content_chart"
            )
        
        elif content_type == 'table':
            table_data = content.get('data', {})
            if isinstance(table_data, dict):
                table_data = pd.DataFrame(table_data)
            
            DataTable.render(
                data=table_data,
                key=f"{key}_content_table"
            )

    @staticmethod
    def _render_report_footer(key: str, result: Dict[str, Any]) -> None:
        """渲染报告底部"""
        st.markdown("---")
        
        Text.subheader("💬 评论和反馈")
        
        # 评论输入
        comment = Input.textarea(
            "添加评论",
            placeholder="请输入您的评论或反馈...",
            key=f"{key}_comment"
        )
        
        col1, col2, col3 = st.columns([1, 1, 3])
        
        with col1:
            if Button.primary("提交评论", key=f"{key}_submit_comment"):
                if comment.strip():
                    Text.success("评论已提交")
                    result['comments_added'] = True
                else:
                    Text.warning("请输入评论内容")
        
        with col2:
            rating = Input.slider(
                "报告评分",
                min_value=1,
                max_value=5,
                value=5,
                key=f"{key}_rating"
            )
        
        # 显示历史评论
        Text.subheader("📝 历史评论")
        
        # 模拟评论数据
        comments = [
            {
                'author': '张三',
                'time': '2小时前',
                'content': '这个报告很详细，数据分析很有价值。',
                'rating': 5
            },
            {
                'author': '李四',
                'time': '1天前',
                'content': '建议增加更多的可视化图表。',
                'rating': 4
            }
        ]
        
        for comment in comments:
            with st.container(border=True):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    Text.markdown(f"**{comment['author']}** - {comment['time']}")
                    Text.markdown(comment['content'])
                
                with col2:
                    stars = "⭐" * comment['rating']
                    Text.caption(f"评分: {stars}")

    @staticmethod
    def _show_export_options(report_data: Dict[str, Any], key: str) -> None:
        """显示导出选项"""
        with st.popover("导出选项", use_container_width=True):
            Text.subheader("📤 导出报告")
            
            # 导出格式
            export_format = Input.selectbox(
                "导出格式",
                ["PDF", "Word", "Excel", "HTML", "JSON"],
                key=f"{key}_export_format"
            )
            
            # 导出内容
            include_charts = Input.checkbox(
                "包含图表",
                value=True,
                key=f"{key}_include_charts"
            )
            
            include_data = Input.checkbox(
                "包含原始数据",
                value=False,
                key=f"{key}_include_data"
            )
            
            include_comments = Input.checkbox(
                "包含评论",
                value=False,
                key=f"{key}_include_comments"
            )
            
            # 导出按钮
            if Button.primary("开始导出", key=f"{key}_start_export"):
                Text.success(f"正在导出为 {export_format} 格式...")
                Text.info("导出功能开发中...")

    @staticmethod
    def email_analysis_report(
        email_data: pd.DataFrame,
        analysis_results: Dict[str, Any],
        key: str = "email_report"
    ) -> Dict[str, Any]:
        """
        邮件分析专用报告
        
        Args:
            email_data: 邮件数据
            analysis_results: 分析结果
            key: 组件唯一标识
            
        Returns:
            Dict[str, Any]: 报告交互结果
        """
        # 构建邮件分析报告数据
        report_data = {
            'title': '📧 邮件分析报告',
            'subtitle': f'分析时间: {datetime.now().strftime("%Y-%m-%d %H:%M")}',
            'type': '邮件分析',
            'author': '智能分析系统',
            'created_date': datetime.now(),
            'summary': {
                'total_records': len(email_data),
                'completion_rate': analysis_results.get('completion_rate', 0),
                'anomalies': analysis_results.get('anomalies', 0),
                'confidence': analysis_results.get('avg_confidence', 0)
            },
            'findings': [
                {
                    'title': '邮件类型分布',
                    'description': '系统识别出多种邮件类型，其中工作报告类邮件占比最高',
                    'importance': 'high',
                    'recommendation': '建议对工作报告类邮件进行进一步细分分析'
                },
                {
                    'title': '发件人活跃度',
                    'description': '发现部分发件人邮件频率异常，可能需要关注',
                    'importance': 'medium',
                    'recommendation': '建议设置邮件频率监控阈值'
                }
            ],
            'charts': [
                {
                    'type': 'pie',
                    'title': '邮件类型分布',
                    'data': email_data.groupby('email_type').size().to_dict() if 'email_type' in email_data.columns else {},
                    'config': {'values': 'count', 'names': 'email_type'}
                }
            ],
            'detailed_data': email_data
        }
        
        return ReportViewer.render(
            report_data=report_data,
            report_type="analysis",
            key=key
        )
