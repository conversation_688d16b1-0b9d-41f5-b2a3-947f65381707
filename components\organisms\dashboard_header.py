#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
仪表盘头部有机体组件

模块描述: 统一的页面头部和导航，包含标题、用户信息、通知、设置等功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..molecules, ..atoms, streamlit, typing
"""

import streamlit as st
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from ..atoms.text import Text
from ..atoms.button import Button
from ..atoms.icon import Icon
from ..atoms.input import Input
from ..molecules.search_box import SearchBox

class DashboardHeader:
    """仪表盘头部有机体组件 - 统一的页面头部和导航"""

    @staticmethod
    def render(
        title: str = "数据分析系统",
        subtitle: str = None,
        user_info: Dict[str, Any] = None,
        navigation_items: List[Dict[str, Any]] = None,
        show_search: bool = True,
        show_notifications: bool = True,
        show_settings: bool = True,
        key: str = "dashboard_header"
    ) -> Dict[str, Any]:
        """
        渲染仪表盘头部
        
        Args:
            title: 主标题
            subtitle: 副标题
            user_info: 用户信息
            navigation_items: 导航项目
            show_search: 是否显示搜索
            show_notifications: 是否显示通知
            show_settings: 是否显示设置
            key: 组件唯一标识
            
        Returns:
            Dict[str, Any]: 用户交互结果
        """
        result = {
            'search_term': '',
            'selected_nav': '',
            'notifications_clicked': False,
            'settings_clicked': False,
            'user_menu_clicked': False
        }

        # 创建头部容器
        with st.container():
            # 主头部行
            col1, col2, col3 = st.columns([3, 4, 3])
            
            # 左侧：标题和导航
            with col1:
                result.update(DashboardHeader._render_title_section(
                    title, subtitle, navigation_items, key
                ))
            
            # 中间：搜索框
            with col2:
                if show_search:
                    search_term, _, _ = SearchBox.render(
                        placeholder="搜索功能、数据、报告...",
                        key=f"{key}_search"
                    )
                    result['search_term'] = search_term
            
            # 右侧：用户操作区
            with col3:
                result.update(DashboardHeader._render_user_section(
                    user_info, show_notifications, show_settings, key
                ))
            
            # 分隔线
            st.markdown("---")
        
        return result

    @staticmethod
    def _render_title_section(
        title: str,
        subtitle: str,
        navigation_items: List[Dict[str, Any]],
        key: str
    ) -> Dict[str, Any]:
        """渲染标题和导航区域"""
        result = {'selected_nav': ''}
        
        # 标题
        Text.title(title)
        if subtitle:
            Text.caption(subtitle)
        
        # 导航菜单
        if navigation_items:
            nav_options = [item.get('label', item.get('name', '')) for item in navigation_items]
            if nav_options:
                selected_nav = Input.selectbox(
                    "导航",
                    nav_options,
                    key=f"{key}_nav",
                    label_visibility="collapsed"
                )
                result['selected_nav'] = selected_nav
        
        return result

    @staticmethod
    def _render_user_section(
        user_info: Dict[str, Any],
        show_notifications: bool,
        show_settings: bool,
        key: str
    ) -> Dict[str, Any]:
        """渲染用户操作区域"""
        result = {
            'notifications_clicked': False,
            'settings_clicked': False,
            'user_menu_clicked': False
        }
        
        # 创建右对齐的列
        cols = st.columns([1, 1, 1, 2])
        
        # 通知按钮
        if show_notifications:
            with cols[0]:
                if Button.secondary("🔔", key=f"{key}_notifications"):
                    result['notifications_clicked'] = True
                    DashboardHeader._show_notifications(key)
        
        # 设置按钮
        if show_settings:
            with cols[1]:
                if Button.secondary("⚙️", key=f"{key}_settings"):
                    result['settings_clicked'] = True
                    DashboardHeader._show_settings(key)
        
        # 帮助按钮
        with cols[2]:
            if Button.secondary("❓", key=f"{key}_help"):
                DashboardHeader._show_help(key)
        
        # 用户菜单
        with cols[3]:
            if user_info:
                user_name = user_info.get('name', '用户')
                user_avatar = user_info.get('avatar', '👤')
                
                if Button.secondary(f"{user_avatar} {user_name}", key=f"{key}_user"):
                    result['user_menu_clicked'] = True
                    DashboardHeader._show_user_menu(user_info, key)
            else:
                if Button.primary("登录", key=f"{key}_login"):
                    Text.info("登录功能开发中...")
        
        return result

    @staticmethod
    def _show_notifications(key: str) -> None:
        """显示通知面板"""
        with st.popover("通知中心", use_container_width=True):
            Text.subheader("📢 系统通知")
            
            # 模拟通知数据
            notifications = [
                {
                    'title': '数据分析完成',
                    'message': '邮件类型分析已完成，共处理1,234封邮件',
                    'time': '5分钟前',
                    'type': 'success'
                },
                {
                    'title': '系统更新',
                    'message': '模块化组件系统已更新到v1.0.0',
                    'time': '1小时前',
                    'type': 'info'
                },
                {
                    'title': '数据同步警告',
                    'message': '部分邮件数据同步失败，请检查网络连接',
                    'time': '2小时前',
                    'type': 'warning'
                }
            ]
            
            for i, notification in enumerate(notifications):
                with st.container(border=True):
                    col1, col2 = st.columns([4, 1])
                    
                    with col1:
                        Text.markdown(f"**{notification['title']}**")
                        Text.caption(notification['message'])
                        Text.caption(f"⏰ {notification['time']}")
                    
                    with col2:
                        if notification['type'] == 'success':
                            Icon.success()
                        elif notification['type'] == 'warning':
                            Icon.warning()
                        else:
                            Icon.info()
            
            if Button.secondary("查看全部通知", key=f"{key}_all_notifications"):
                Text.info("通知中心功能开发中...")

    @staticmethod
    def _show_settings(key: str) -> None:
        """显示设置面板"""
        with st.popover("系统设置", use_container_width=True):
            Text.subheader("⚙️ 系统设置")
            
            # 主题设置
            theme = Input.selectbox(
                "界面主题",
                ["自动", "浅色", "深色"],
                key=f"{key}_theme"
            )
            
            # 语言设置
            language = Input.selectbox(
                "界面语言",
                ["中文", "English"],
                key=f"{key}_language"
            )
            
            # 通知设置
            notifications_enabled = Input.checkbox(
                "启用通知",
                value=True,
                key=f"{key}_notifications_enabled"
            )
            
            # 自动刷新设置
            auto_refresh = Input.checkbox(
                "自动刷新数据",
                value=False,
                key=f"{key}_auto_refresh"
            )
            
            if auto_refresh:
                refresh_interval = Input.slider(
                    "刷新间隔（秒）",
                    min_value=30,
                    max_value=300,
                    value=60,
                    key=f"{key}_refresh_interval"
                )
            
            st.markdown("---")
            
            col1, col2 = st.columns(2)
            with col1:
                if Button.primary("保存设置", key=f"{key}_save_settings"):
                    Text.success("设置已保存")
            
            with col2:
                if Button.secondary("重置", key=f"{key}_reset_settings"):
                    Text.info("设置已重置")

    @staticmethod
    def _show_help(key: str) -> None:
        """显示帮助信息"""
        with st.popover("帮助中心", use_container_width=True):
            Text.subheader("❓ 帮助中心")
            
            help_topics = [
                {
                    'title': '快速开始',
                    'description': '了解如何使用数据分析系统',
                    'icon': '🚀'
                },
                {
                    'title': '功能介绍',
                    'description': '详细了解各个功能模块',
                    'icon': '📖'
                },
                {
                    'title': '常见问题',
                    'description': '查看常见问题和解决方案',
                    'icon': '❓'
                },
                {
                    'title': '联系支持',
                    'description': '获取技术支持和帮助',
                    'icon': '📞'
                }
            ]
            
            for topic in help_topics:
                with st.container(border=True):
                    col1, col2 = st.columns([1, 4])
                    
                    with col1:
                        Text.markdown(f"<div style='font-size: 2rem;'>{topic['icon']}</div>", 
                                     unsafe_allow_html=True)
                    
                    with col2:
                        if Button.secondary(topic['title'], use_container_width=True):
                            Text.info(f"{topic['description']} - 功能开发中...")
                        Text.caption(topic['description'])

    @staticmethod
    def _show_user_menu(user_info: Dict[str, Any], key: str) -> None:
        """显示用户菜单"""
        with st.popover("用户菜单", use_container_width=True):
            # 用户信息
            Text.subheader(f"👤 {user_info.get('name', '用户')}")
            Text.caption(f"📧 {user_info.get('email', '<EMAIL>')}")
            Text.caption(f"🏢 {user_info.get('department', '技术部')}")
            
            st.markdown("---")
            
            # 菜单项
            menu_items = [
                {'label': '个人资料', 'icon': '👤'},
                {'label': '账户设置', 'icon': '⚙️'},
                {'label': '使用统计', 'icon': '📊'},
                {'label': '帮助文档', 'icon': '📖'},
                {'label': '意见反馈', 'icon': '💬'},
                {'label': '退出登录', 'icon': '🚪'}
            ]
            
            for item in menu_items:
                if Button.secondary(
                    f"{item['icon']} {item['label']}", 
                    key=f"{key}_menu_{item['label']}",
                    use_container_width=True
                ):
                    if item['label'] == '退出登录':
                        Text.warning("确认退出登录？")
                    else:
                        Text.info(f"{item['label']} 功能开发中...")

    @staticmethod
    def breadcrumb_header(
        breadcrumbs: List[Dict[str, str]],
        current_page: str,
        key: str = "breadcrumb_header"
    ) -> None:
        """
        面包屑导航头部
        
        Args:
            breadcrumbs: 面包屑路径
            current_page: 当前页面
            key: 组件唯一标识
        """
        # 面包屑导航
        breadcrumb_text = " > ".join([
            f"[{item['label']}]({item.get('url', '#')})" 
            for item in breadcrumbs
        ])
        breadcrumb_text += f" > **{current_page}**"
        
        Text.markdown(breadcrumb_text)
        st.markdown("---")

    @staticmethod
    def status_header(
        title: str,
        status: str,
        last_update: datetime = None,
        key: str = "status_header"
    ) -> None:
        """
        状态显示头部
        
        Args:
            title: 标题
            status: 状态
            last_update: 最后更新时间
            key: 组件唯一标识
        """
        col1, col2, col3 = st.columns([3, 2, 2])
        
        with col1:
            Text.header(title)
        
        with col2:
            # 状态指示器
            status_config = {
                '正常': {'color': 'green', 'icon': '🟢'},
                '警告': {'color': 'orange', 'icon': '🟡'},
                '错误': {'color': 'red', 'icon': '🔴'},
                '离线': {'color': 'gray', 'icon': '⚫'}
            }
            
            config = status_config.get(status, status_config['离线'])
            Text.markdown(f"{config['icon']} **状态**: {status}")
        
        with col3:
            if last_update:
                Text.caption(f"⏰ 更新时间: {last_update.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                Text.caption("⏰ 更新时间: 未知")
        
        st.markdown("---")

    @staticmethod
    def action_header(
        title: str,
        actions: List[Dict[str, Any]],
        key: str = "action_header"
    ) -> Dict[str, bool]:
        """
        操作按钮头部
        
        Args:
            title: 标题
            actions: 操作按钮配置
            key: 组件唯一标识
            
        Returns:
            Dict[str, bool]: 按钮点击状态
        """
        result = {}
        
        col1, col2 = st.columns([3, 2])
        
        with col1:
            Text.header(title)
        
        with col2:
            # 操作按钮
            if len(actions) <= 3:
                cols = st.columns(len(actions))
                for i, action in enumerate(actions):
                    with cols[i]:
                        button_type = action.get('type', 'secondary')
                        if button_type == 'primary':
                            clicked = Button.primary(
                                action['label'],
                                key=f"{key}_action_{i}"
                            )
                        else:
                            clicked = Button.secondary(
                                action['label'],
                                key=f"{key}_action_{i}"
                            )
                        
                        result[action['label']] = clicked
            else:
                # 下拉菜单形式
                selected_action = Input.selectbox(
                    "选择操作",
                    [action['label'] for action in actions],
                    key=f"{key}_action_select"
                )
                
                if Button.primary("执行", key=f"{key}_execute"):
                    result[selected_action] = True
        
        st.markdown("---")
        
        return result
