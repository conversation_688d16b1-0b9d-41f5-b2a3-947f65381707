#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
有机体组件模块包

模块描述: 由分子组件组合而成的复杂功能组件，提供完整的业务功能模块
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..molecules, ..atoms, streamlit, typing
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 有机体组件导入
from .analysis_panel import AnalysisPanel
from .dashboard_header import DashboardHeader
from .navigation_sidebar import NavigationSidebar
from .report_viewer import ReportViewer

__all__ = [
    # 分析面板
    'AnalysisPanel',
    # 仪表盘头部
    'DashboardHeader',
    # 导航侧边栏
    'NavigationSidebar',
    # 报告查看器
    'ReportViewer'
]
