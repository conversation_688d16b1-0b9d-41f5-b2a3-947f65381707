#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
导航侧边栏有机体组件

模块描述: 多级导航和菜单系统，支持分组、搜索、收藏等功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..molecules, ..atoms, streamlit, typing
"""

import streamlit as st
from typing import Dict, Any, List, Optional, Callable
from ..atoms.text import Text
from ..atoms.button import Button
from ..atoms.icon import Icon
from ..atoms.input import Input
from ..molecules.search_box import SearchBox

class NavigationSidebar:
    """导航侧边栏有机体组件 - 多级导航和菜单系统"""

    @staticmethod
    def render(
        navigation_config: List[Dict[str, Any]],
        current_page: str = "",
        show_search: bool = True,
        show_favorites: bool = True,
        collapsible: bool = True,
        key: str = "navigation_sidebar"
    ) -> Dict[str, Any]:
        """
        渲染导航侧边栏
        
        Args:
            navigation_config: 导航配置
            current_page: 当前页面
            show_search: 是否显示搜索
            show_favorites: 是否显示收藏
            collapsible: 是否可折叠
            key: 组件唯一标识
            
        Returns:
            Dict[str, Any]: 导航结果
        """
        result = {
            'selected_page': current_page,
            'search_term': '',
            'favorites_updated': False
        }

        with st.sidebar:
            # 侧边栏头部
            NavigationSidebar._render_sidebar_header(key)
            
            # 搜索功能
            if show_search:
                search_term, _, _ = SearchBox.render(
                    placeholder="搜索功能...",
                    key=f"{key}_search"
                )
                result['search_term'] = search_term
                
                # 过滤导航项
                if search_term:
                    navigation_config = NavigationSidebar._filter_navigation(
                        navigation_config, search_term
                    )
            
            # 收藏功能
            if show_favorites:
                NavigationSidebar._render_favorites(key)
            
            # 主导航菜单
            selected_page = NavigationSidebar._render_navigation_menu(
                navigation_config, current_page, collapsible, key
            )
            result['selected_page'] = selected_page
            
            # 侧边栏底部
            NavigationSidebar._render_sidebar_footer(key)
        
        return result

    @staticmethod
    def _render_sidebar_header(key: str) -> None:
        """渲染侧边栏头部"""
        Text.title("📊 数据分析")
        Text.caption("智能邮件分析系统")
        st.markdown("---")

    @staticmethod
    def _render_favorites(key: str) -> None:
        """渲染收藏功能"""
        with st.expander("⭐ 收藏夹", expanded=False):
            # 初始化收藏夹
            if f"{key}_favorites" not in st.session_state:
                st.session_state[f"{key}_favorites"] = [
                    "邮件分析",
                    "数据概览"
                ]
            
            favorites = st.session_state[f"{key}_favorites"]
            
            if favorites:
                for favorite in favorites:
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        if Button.secondary(f"⭐ {favorite}", key=f"{key}_fav_{favorite}"):
                            st.session_state['current_page'] = favorite
                            st.rerun()
                    
                    with col2:
                        if Button.secondary("❌", key=f"{key}_remove_{favorite}"):
                            st.session_state[f"{key}_favorites"].remove(favorite)
                            st.rerun()
            else:
                Text.caption("暂无收藏项")

    @staticmethod
    def _render_navigation_menu(
        navigation_config: List[Dict[str, Any]],
        current_page: str,
        collapsible: bool,
        key: str
    ) -> str:
        """渲染导航菜单"""
        selected_page = current_page
        
        for group in navigation_config:
            group_name = group.get('name', '未命名分组')
            group_icon = group.get('icon', '📁')
            items = group.get('items', [])
            expanded = group.get('expanded', True)
            
            # 分组标题
            if collapsible:
                with st.expander(f"{group_icon} {group_name}", expanded=expanded):
                    for item in items:
                        page_selected = NavigationSidebar._render_navigation_item(
                            item, current_page, key
                        )
                        if page_selected:
                            selected_page = page_selected
            else:
                Text.subheader(f"{group_icon} {group_name}")
                for item in items:
                    page_selected = NavigationSidebar._render_navigation_item(
                        item, current_page, key
                    )
                    if page_selected:
                        selected_page = page_selected
                st.markdown("---")
        
        return selected_page

    @staticmethod
    def _render_navigation_item(
        item: Dict[str, Any],
        current_page: str,
        key: str
    ) -> Optional[str]:
        """渲染单个导航项"""
        name = item.get('name', '未命名')
        icon = item.get('icon', '📄')
        url = item.get('url', '')
        badge = item.get('badge', '')
        disabled = item.get('disabled', False)
        children = item.get('children', [])
        
        # 检查是否为当前页面
        is_current = current_page == name
        
        # 主导航项
        col1, col2, col3 = st.columns([3, 1, 1])
        
        with col1:
            button_type = "primary" if is_current else "secondary"
            if button_type == "primary":
                clicked = Button.primary(
                    f"{icon} {name}",
                    key=f"{key}_nav_{name}",
                    disabled=disabled
                )
            else:
                clicked = Button.secondary(
                    f"{icon} {name}",
                    key=f"{key}_nav_{name}",
                    disabled=disabled
                )
            
            if clicked and not disabled:
                return name
        
        with col2:
            # 徽章
            if badge:
                Text.caption(f"🔴 {badge}")
        
        with col3:
            # 收藏按钮
            if Button.secondary("⭐", key=f"{key}_star_{name}"):
                favorites_key = f"{key}_favorites"
                if favorites_key not in st.session_state:
                    st.session_state[favorites_key] = []
                
                if name not in st.session_state[favorites_key]:
                    st.session_state[favorites_key].append(name)
                    Text.success(f"已收藏 {name}")
                    st.rerun()
        
        # 子导航项
        if children and is_current:
            for child in children:
                child_name = child.get('name', '未命名')
                child_icon = child.get('icon', '📄')
                
                if Button.secondary(
                    f"  {child_icon} {child_name}",
                    key=f"{key}_child_{child_name}"
                ):
                    return child_name
        
        return None

    @staticmethod
    def _filter_navigation(
        navigation_config: List[Dict[str, Any]],
        search_term: str
    ) -> List[Dict[str, Any]]:
        """过滤导航项"""
        filtered_config = []
        
        for group in navigation_config:
            filtered_items = []
            
            for item in group.get('items', []):
                # 检查项目名称是否匹配
                if search_term.lower() in item.get('name', '').lower():
                    filtered_items.append(item)
                # 检查子项目
                elif item.get('children'):
                    filtered_children = [
                        child for child in item['children']
                        if search_term.lower() in child.get('name', '').lower()
                    ]
                    if filtered_children:
                        filtered_item = item.copy()
                        filtered_item['children'] = filtered_children
                        filtered_items.append(filtered_item)
            
            if filtered_items:
                filtered_group = group.copy()
                filtered_group['items'] = filtered_items
                filtered_config.append(filtered_group)
        
        return filtered_config

    @staticmethod
    def _render_sidebar_footer(key: str) -> None:
        """渲染侧边栏底部"""
        st.markdown("---")
        
        # 系统状态
        Text.caption("🟢 系统状态: 正常")
        Text.caption("📊 数据更新: 实时")
        Text.caption("👥 在线用户: 5")
        
        st.markdown("---")
        
        # 快捷操作
        Text.caption("快捷操作:")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if Button.secondary("🔄", key=f"{key}_refresh"):
                Text.success("数据已刷新")
        
        with col2:
            if Button.secondary("📤", key=f"{key}_export"):
                Text.info("导出功能开发中...")

    @staticmethod
    def email_navigation() -> List[Dict[str, Any]]:
        """邮件分析系统专用导航配置"""
        return [
            {
                'name': '概览',
                'icon': '📊',
                'expanded': True,
                'items': [
                    {
                        'name': '数据概览',
                        'icon': '📈',
                        'url': '/overview'
                    },
                    {
                        'name': '实时监控',
                        'icon': '⏱️',
                        'url': '/monitor',
                        'badge': 'NEW'
                    }
                ]
            },
            {
                'name': '邮件分析',
                'icon': '📧',
                'expanded': True,
                'items': [
                    {
                        'name': '邮件分析',
                        'icon': '🔍',
                        'url': '/email-analysis',
                        'children': [
                            {'name': '类型分析', 'icon': '📊'},
                            {'name': '发件人分析', 'icon': '👤'},
                            {'name': '时间分析', 'icon': '⏰'}
                        ]
                    },
                    {
                        'name': '附件管理',
                        'icon': '📎',
                        'url': '/attachments'
                    },
                    {
                        'name': '邮件下载',
                        'icon': '⬇️',
                        'url': '/download'
                    }
                ]
            },
            {
                'name': 'AI分析',
                'icon': '🤖',
                'expanded': False,
                'items': [
                    {
                        'name': '智能分类',
                        'icon': '🏷️',
                        'url': '/ai-classification'
                    },
                    {
                        'name': '情感分析',
                        'icon': '😊',
                        'url': '/sentiment'
                    },
                    {
                        'name': '关键词提取',
                        'icon': '🔑',
                        'url': '/keywords'
                    },
                    {
                        'name': '模型管理',
                        'icon': '⚙️',
                        'url': '/models'
                    }
                ]
            },
            {
                'name': '报告',
                'icon': '📋',
                'expanded': False,
                'items': [
                    {
                        'name': '分析报告',
                        'icon': '📊',
                        'url': '/reports'
                    },
                    {
                        'name': '导出数据',
                        'icon': '📤',
                        'url': '/export'
                    },
                    {
                        'name': '定时任务',
                        'icon': '⏰',
                        'url': '/scheduled'
                    }
                ]
            },
            {
                'name': '系统管理',
                'icon': '⚙️',
                'expanded': False,
                'items': [
                    {
                        'name': '用户管理',
                        'icon': '👥',
                        'url': '/users'
                    },
                    {
                        'name': '系统设置',
                        'icon': '🔧',
                        'url': '/settings'
                    },
                    {
                        'name': '日志查看',
                        'icon': '📝',
                        'url': '/logs'
                    },
                    {
                        'name': '系统监控',
                        'icon': '📡',
                        'url': '/system-monitor'
                    }
                ]
            }
        ]

    @staticmethod
    def compact_navigation(
        navigation_config: List[Dict[str, Any]],
        key: str = "compact_nav"
    ) -> str:
        """
        紧凑型导航
        
        Args:
            navigation_config: 导航配置
            key: 组件唯一标识
            
        Returns:
            str: 选中的页面
        """
        # 扁平化所有导航项
        all_items = []
        for group in navigation_config:
            for item in group.get('items', []):
                all_items.append({
                    'name': item.get('name', ''),
                    'icon': item.get('icon', '📄'),
                    'group': group.get('name', '')
                })
                
                # 添加子项目
                for child in item.get('children', []):
                    all_items.append({
                        'name': child.get('name', ''),
                        'icon': child.get('icon', '📄'),
                        'group': f"{group.get('name', '')} > {item.get('name', '')}"
                    })
        
        # 选择框形式的导航
        options = [f"{item['icon']} {item['name']}" for item in all_items]
        
        selected = Input.selectbox(
            "导航",
            options,
            key=f"{key}_compact_select"
        )
        
        # 返回选中的页面名称
        if selected:
            for item in all_items:
                if f"{item['icon']} {item['name']}" == selected:
                    return item['name']
        
        return ""

    @staticmethod
    def breadcrumb_navigation(
        current_path: List[str],
        navigation_config: List[Dict[str, Any]],
        key: str = "breadcrumb_nav"
    ) -> None:
        """
        面包屑导航
        
        Args:
            current_path: 当前路径
            navigation_config: 导航配置
            key: 组件唯一标识
        """
        if not current_path:
            return
        
        # 构建面包屑
        breadcrumb_items = []
        for i, path_item in enumerate(current_path):
            if i < len(current_path) - 1:
                # 可点击的路径项
                if Button.secondary(path_item, key=f"{key}_breadcrumb_{i}"):
                    # 导航到该路径
                    st.session_state['current_path'] = current_path[:i+1]
                    st.rerun()
            else:
                # 当前页面（不可点击）
                Text.markdown(f"**{path_item}**")
            
            if i < len(current_path) - 1:
                Text.markdown(" > ", unsafe_allow_html=True)
