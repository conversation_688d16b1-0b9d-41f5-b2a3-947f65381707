#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
按钮原子组件

模块描述: 提供统一的按钮接口，支持多种类型和大小的按钮
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: streamlit, typing, enum
"""

import streamlit as st
from typing import Optional, Callable, Dict, Any
from enum import Enum

class ButtonType(Enum):
    """按钮类型枚举"""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    SUCCESS = "success"
    WARNING = "warning"
    DANGER = "danger"
    INFO = "info"

class ButtonSize(Enum):
    """按钮大小枚举"""
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"

class Button:
    """按钮原子组件 - 提供统一的按钮接口"""

    @staticmethod
    def render(
        label: str,
        button_type: ButtonType = ButtonType.PRIMARY,
        size: ButtonSize = ButtonSize.MEDIUM,
        disabled: bool = False,
        key: str = None,
        on_click: Callable = None,
        help: str = None,
        use_container_width: bool = False,
        **kwargs
    ) -> bool:
        """
        渲染按钮
        
        Args:
            label: 按钮文本
            button_type: 按钮类型
            size: 按钮大小
            disabled: 是否禁用
            key: 组件唯一标识
            on_click: 点击回调函数
            help: 帮助文本
            use_container_width: 是否使用容器宽度
            **kwargs: 其他参数
            
        Returns:
            bool: 是否被点击
        """
        # 根据类型设置Streamlit按钮类型
        st_type = "primary" if button_type == ButtonType.PRIMARY else "secondary"
        
        # 根据大小设置CSS样式
        if size != ButtonSize.MEDIUM:
            css_style = Button._get_size_style(size)
            st.markdown(css_style, unsafe_allow_html=True)
        
        # 渲染按钮
        return st.button(
            label,
            key=key,
            disabled=disabled,
            type=st_type,
            on_click=on_click,
            help=help,
            use_container_width=use_container_width,
            **kwargs
        )

    @staticmethod
    def primary(label: str, **kwargs) -> bool:
        """主要按钮"""
        return Button.render(label, ButtonType.PRIMARY, **kwargs)

    @staticmethod
    def secondary(label: str, **kwargs) -> bool:
        """次要按钮"""
        return Button.render(label, ButtonType.SECONDARY, **kwargs)

    @staticmethod
    def success(label: str, **kwargs) -> bool:
        """成功按钮"""
        return Button.render(label, ButtonType.SUCCESS, **kwargs)

    @staticmethod
    def warning(label: str, **kwargs) -> bool:
        """警告按钮"""
        return Button.render(label, ButtonType.WARNING, **kwargs)

    @staticmethod
    def danger(label: str, **kwargs) -> bool:
        """危险按钮"""
        return Button.render(label, ButtonType.DANGER, **kwargs)

    @staticmethod
    def info(label: str, **kwargs) -> bool:
        """信息按钮"""
        return Button.render(label, ButtonType.INFO, **kwargs)

    @staticmethod
    def _get_size_style(size: ButtonSize) -> str:
        """获取按钮大小样式"""
        if size == ButtonSize.SMALL:
            return """
            <style>
            .stButton > button {
                height: 2rem;
                padding: 0.25rem 0.75rem;
                font-size: 0.875rem;
            }
            </style>
            """
        elif size == ButtonSize.LARGE:
            return """
            <style>
            .stButton > button {
                height: 3rem;
                padding: 0.75rem 1.5rem;
                font-size: 1.125rem;
            }
            </style>
            """
        return ""

    @staticmethod
    def with_icon(
        label: str, 
        icon: str, 
        icon_position: str = "left",
        **kwargs
    ) -> bool:
        """
        带图标的按钮
        
        Args:
            label: 按钮文本
            icon: 图标名称（emoji或Unicode）
            icon_position: 图标位置（left/right）
            **kwargs: 其他按钮参数
            
        Returns:
            bool: 是否被点击
        """
        if icon_position == "left":
            display_label = f"{icon} {label}"
        else:
            display_label = f"{label} {icon}"
            
        return Button.render(display_label, **kwargs)
