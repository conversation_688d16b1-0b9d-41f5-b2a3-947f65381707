#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
图标原子组件

模块描述: 提供统一的图标显示接口，支持多种图标类型和样式
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: streamlit, typing, enum
"""

import streamlit as st
from typing import Optional, Dict
from enum import Enum

class IconType(Enum):
    """图标类型枚举"""
    EMOJI = "emoji"
    UNICODE = "unicode"
    MATERIAL = "material"
    FONTAWESOME = "fontawesome"

class Icon:
    """图标原子组件 - 提供统一的图标显示接口"""

    # 常用图标映射
    COMMON_ICONS = {
        # 操作图标
        "add": "➕",
        "edit": "✏️",
        "delete": "🗑️",
        "save": "💾",
        "cancel": "❌",
        "confirm": "✅",
        "search": "🔍",
        "filter": "🔽",
        "sort": "🔄",
        "refresh": "🔄",
        "download": "⬇️",
        "upload": "⬆️",
        "export": "📤",
        "import": "📥",
        
        # 状态图标
        "success": "✅",
        "error": "❌",
        "warning": "⚠️",
        "info": "ℹ️",
        "loading": "⏳",
        "pending": "⏸️",
        "running": "▶️",
        "stopped": "⏹️",
        
        # 导航图标
        "home": "🏠",
        "back": "⬅️",
        "forward": "➡️",
        "up": "⬆️",
        "down": "⬇️",
        "left": "⬅️",
        "right": "➡️",
        "menu": "☰",
        "close": "✖️",
        
        # 业务图标
        "user": "👤",
        "users": "👥",
        "email": "📧",
        "report": "📊",
        "chart": "📈",
        "calendar": "📅",
        "clock": "🕐",
        "folder": "📁",
        "file": "📄",
        "settings": "⚙️",
        "help": "❓",
        "star": "⭐",
        "heart": "❤️",
        "bookmark": "🔖",
        "tag": "🏷️",
        "flag": "🚩",
        
        # 分析图标
        "analytics": "📊",
        "trend_up": "📈",
        "trend_down": "📉",
        "pie_chart": "🥧",
        "bar_chart": "📊",
        "line_chart": "📈",
        "scatter_plot": "🔸",
        "dashboard": "📋",
        "metrics": "📏",
        "kpi": "🎯",
        
        # 系统图标
        "database": "🗄️",
        "server": "🖥️",
        "cloud": "☁️",
        "network": "🌐",
        "security": "🔒",
        "key": "🔑",
        "shield": "🛡️",
        "bug": "🐛",
        "code": "💻",
        "api": "🔌"
    }

    @staticmethod
    def render(
        name: str, 
        size: str = "medium", 
        color: str = None,
        title: str = None,
        style: Dict[str, str] = None
    ) -> None:
        """
        渲染图标
        
        Args:
            name: 图标名称或直接的图标字符
            size: 图标大小 (small/medium/large)
            color: 图标颜色
            title: 图标标题
            style: 自定义样式
        """
        # 获取图标字符
        icon_char = Icon._get_icon_char(name)
        
        # 设置大小样式
        size_style = Icon._get_size_style(size)
        
        # 设置颜色样式
        color_style = f"color: {color};" if color else ""
        
        # 合并样式
        combined_style = f"{size_style} {color_style}"
        if style:
            style_str = "; ".join([f"{k}: {v}" for k, v in style.items()])
            combined_style += f" {style_str}"
        
        # 渲染图标
        if combined_style.strip():
            html = f'<span style="{combined_style}" title="{title or name}">{icon_char}</span>'
            st.markdown(html, unsafe_allow_html=True)
        else:
            st.markdown(f"{icon_char}", help=title)

    @staticmethod
    def _get_icon_char(name: str) -> str:
        """获取图标字符"""
        # 如果是已知的图标名称，返回对应的字符
        if name in Icon.COMMON_ICONS:
            return Icon.COMMON_ICONS[name]
        
        # 如果是单个字符（可能是emoji或unicode），直接返回
        if len(name) <= 4:  # emoji通常是1-4个字符
            return name
        
        # 默认返回问号图标
        return "❓"

    @staticmethod
    def _get_size_style(size: str) -> str:
        """获取大小样式"""
        size_map = {
            "small": "font-size: 0.875rem;",
            "medium": "font-size: 1rem;",
            "large": "font-size: 1.25rem;",
            "xl": "font-size: 1.5rem;",
            "2xl": "font-size: 2rem;"
        }
        return size_map.get(size, size_map["medium"])

    @staticmethod
    def success(size: str = "medium", **kwargs) -> None:
        """成功图标"""
        Icon.render("success", size=size, color="green", **kwargs)

    @staticmethod
    def error(size: str = "medium", **kwargs) -> None:
        """错误图标"""
        Icon.render("error", size=size, color="red", **kwargs)

    @staticmethod
    def warning(size: str = "medium", **kwargs) -> None:
        """警告图标"""
        Icon.render("warning", size=size, color="orange", **kwargs)

    @staticmethod
    def info(size: str = "medium", **kwargs) -> None:
        """信息图标"""
        Icon.render("info", size=size, color="blue", **kwargs)

    @staticmethod
    def loading(size: str = "medium", **kwargs) -> None:
        """加载图标"""
        Icon.render("loading", size=size, **kwargs)

    @staticmethod
    def user(size: str = "medium", **kwargs) -> None:
        """用户图标"""
        Icon.render("user", size=size, **kwargs)

    @staticmethod
    def chart(size: str = "medium", **kwargs) -> None:
        """图表图标"""
        Icon.render("chart", size=size, **kwargs)

    @staticmethod
    def dashboard(size: str = "medium", **kwargs) -> None:
        """仪表盘图标"""
        Icon.render("dashboard", size=size, **kwargs)

    @staticmethod
    def settings(size: str = "medium", **kwargs) -> None:
        """设置图标"""
        Icon.render("settings", size=size, **kwargs)

    @staticmethod
    def help(size: str = "medium", **kwargs) -> None:
        """帮助图标"""
        Icon.render("help", size=size, **kwargs)

    @staticmethod
    def get_available_icons() -> Dict[str, str]:
        """获取可用图标列表"""
        return Icon.COMMON_ICONS.copy()

    @staticmethod
    def display_icon_gallery() -> None:
        """显示图标画廊"""
        st.subheader("可用图标")
        
        # 按类别分组显示
        categories = {
            "操作图标": ["add", "edit", "delete", "save", "cancel", "confirm", "search", "filter"],
            "状态图标": ["success", "error", "warning", "info", "loading", "pending"],
            "导航图标": ["home", "back", "forward", "up", "down", "menu", "close"],
            "业务图标": ["user", "users", "email", "report", "chart", "calendar", "settings"],
            "分析图标": ["analytics", "trend_up", "trend_down", "dashboard", "metrics", "kpi"]
        }
        
        for category, icons in categories.items():
            st.write(f"**{category}**")
            cols = st.columns(len(icons))
            for i, icon_name in enumerate(icons):
                with cols[i]:
                    Icon.render(icon_name)
                    st.caption(icon_name)
