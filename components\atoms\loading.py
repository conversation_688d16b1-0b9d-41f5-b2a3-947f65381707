#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
加载原子组件

模块描述: 提供统一的加载状态显示接口，支持多种加载样式
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: streamlit, typing, enum, time
"""

import streamlit as st
import time
from typing import Optional, Any, Callable
from enum import Enum
from contextlib import contextmanager

class LoadingType(Enum):
    """加载类型枚举"""
    SPINNER = "spinner"
    PROGRESS = "progress"
    STATUS = "status"
    PLACEHOLDER = "placeholder"

class Loading:
    """加载原子组件 - 提供统一的加载状态显示接口"""

    @staticmethod
    @contextmanager
    def spinner(text: str = "加载中..."):
        """
        加载旋转器上下文管理器
        
        Args:
            text: 加载文本
            
        Usage:
            with Loading.spinner("正在处理..."):
                # 执行耗时操作
                time.sleep(2)
        """
        with st.spinner(text):
            yield

    @staticmethod
    def progress_bar(
        value: float = 0.0,
        text: str = None,
        key: str = None
    ):
        """
        进度条
        
        Args:
            value: 进度值 (0.0-1.0)
            text: 进度文本
            key: 组件唯一标识
            
        Returns:
            progress_bar: 进度条对象
        """
        return st.progress(value, text=text)

    @staticmethod
    @contextmanager
    def status(
        label: str,
        state: str = "running",
        expanded: bool = False
    ):
        """
        状态显示上下文管理器
        
        Args:
            label: 状态标签
            state: 状态类型 (running/complete/error)
            expanded: 是否展开
            
        Usage:
            with Loading.status("正在分析数据", state="running") as status:
                # 执行操作
                status.write("步骤1: 数据预处理")
                time.sleep(1)
                status.write("步骤2: 模型训练")
                time.sleep(1)
        """
        with st.status(label, state=state, expanded=expanded) as status:
            yield status

    @staticmethod
    def placeholder(key: str = None):
        """
        占位符
        
        Args:
            key: 组件唯一标识
            
        Returns:
            placeholder: 占位符对象
        """
        return st.empty()

    @staticmethod
    def skeleton(
        height: int = 100,
        lines: int = 3,
        key: str = None
    ) -> None:
        """
        骨架屏加载效果
        
        Args:
            height: 高度
            lines: 行数
            key: 组件唯一标识
        """
        # 使用CSS创建骨架屏效果
        skeleton_css = f"""
        <style>
        .skeleton {{
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
            margin: 4px 0;
        }}
        
        @keyframes loading {{
            0% {{ background-position: 200% 0; }}
            100% {{ background-position: -200% 0; }}
        }}
        </style>
        """
        
        st.markdown(skeleton_css, unsafe_allow_html=True)
        
        # 创建骨架屏元素
        skeleton_html = ""
        for i in range(lines):
            line_height = height // lines
            width = 100 if i < lines - 1 else 60  # 最后一行短一些
            skeleton_html += f'<div class="skeleton" style="height: {line_height}px; width: {width}%;"></div>'
        
        st.markdown(skeleton_html, unsafe_allow_html=True)

    @staticmethod
    def dots(text: str = "加载中", duration: float = 2.0) -> None:
        """
        点点点加载动画
        
        Args:
            text: 基础文本
            duration: 动画持续时间（秒）
        """
        placeholder = st.empty()
        
        start_time = time.time()
        while time.time() - start_time < duration:
            for dots in ["", ".", "..", "..."]:
                placeholder.text(f"{text}{dots}")
                time.sleep(0.3)
                if time.time() - start_time >= duration:
                    break
        
        placeholder.empty()

    @staticmethod
    def with_loading(
        func: Callable,
        loading_text: str = "处理中...",
        loading_type: LoadingType = LoadingType.SPINNER,
        *args,
        **kwargs
    ) -> Any:
        """
        装饰器式加载
        
        Args:
            func: 要执行的函数
            loading_text: 加载文本
            loading_type: 加载类型
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
        """
        if loading_type == LoadingType.SPINNER:
            with Loading.spinner(loading_text):
                return func(*args, **kwargs)
        elif loading_type == LoadingType.STATUS:
            with Loading.status(loading_text) as status:
                result = func(*args, **kwargs)
                status.update(label=f"{loading_text} - 完成", state="complete")
                return result
        else:
            return func(*args, **kwargs)

    @staticmethod
    def simulate_progress(
        total_steps: int,
        step_duration: float = 0.1,
        text_template: str = "步骤 {current}/{total}",
        key: str = None
    ) -> None:
        """
        模拟进度条
        
        Args:
            total_steps: 总步骤数
            step_duration: 每步持续时间
            text_template: 文本模板
            key: 组件唯一标识
        """
        progress_bar = Loading.progress_bar(0.0, key=key)
        
        for i in range(total_steps + 1):
            progress = i / total_steps
            text = text_template.format(current=i, total=total_steps)
            progress_bar.progress(progress, text=text)
            
            if i < total_steps:
                time.sleep(step_duration)

    @staticmethod
    def loading_message(
        message: str,
        icon: str = "⏳",
        color: str = "blue"
    ) -> None:
        """
        加载消息
        
        Args:
            message: 消息内容
            icon: 图标
            color: 颜色
        """
        if color == "blue":
            st.info(f"{icon} {message}")
        elif color == "yellow":
            st.warning(f"{icon} {message}")
        elif color == "green":
            st.success(f"{icon} {message}")
        else:
            st.write(f"{icon} {message}")

    @staticmethod
    def loading_card(
        title: str,
        description: str = None,
        progress: float = None,
        icon: str = "⏳"
    ) -> None:
        """
        加载卡片
        
        Args:
            title: 标题
            description: 描述
            progress: 进度值
            icon: 图标
        """
        with st.container(border=True):
            col1, col2 = st.columns([1, 10])
            
            with col1:
                st.markdown(f"<div style='font-size: 2rem;'>{icon}</div>", 
                           unsafe_allow_html=True)
            
            with col2:
                st.subheader(title)
                if description:
                    st.caption(description)
                if progress is not None:
                    st.progress(progress)

    @staticmethod
    def batch_loading(
        items: list,
        process_func: Callable,
        batch_size: int = 10,
        loading_text: str = "批量处理中..."
    ) -> list:
        """
        批量处理加载
        
        Args:
            items: 要处理的项目列表
            process_func: 处理函数
            batch_size: 批次大小
            loading_text: 加载文本
            
        Returns:
            处理结果列表
        """
        results = []
        total_items = len(items)
        
        progress_bar = Loading.progress_bar(0.0)
        
        for i in range(0, total_items, batch_size):
            batch = items[i:i + batch_size]
            
            # 更新进度
            progress = (i + len(batch)) / total_items
            text = f"{loading_text} ({i + len(batch)}/{total_items})"
            progress_bar.progress(progress, text=text)
            
            # 处理批次
            batch_results = [process_func(item) for item in batch]
            results.extend(batch_results)
        
        progress_bar.progress(1.0, text="处理完成")
        return results
