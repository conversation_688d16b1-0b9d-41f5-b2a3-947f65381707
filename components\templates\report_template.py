#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
报告模板组件

模块描述: 报告生成和展示模板，支持多种报告格式和自动生成
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..organisms, streamlit, typing
"""

import streamlit as st
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from ..organisms.report_viewer import ReportViewer
from ..organisms.dashboard_header import DashboardHeader
from ..molecules.metric_card import MetricCard
from ..molecules.chart_container import ChartContainer

class ReportTemplate:
    """报告模板组件 - 报告生成和展示模板"""

    @staticmethod
    def render(
        report_type: str,
        data: Dict[str, Any],
        config: Dict[str, Any] = None,
        key: str = "report_template"
    ) -> Dict[str, Any]:
        """
        渲染报告模板
        
        Args:
            report_type: 报告类型
            data: 报告数据
            config: 报告配置
            key: 组件唯一标识
            
        Returns:
            Dict[str, Any]: 报告交互结果
        """
        config = config or {}
        
        # 页面配置
        st.set_page_config(
            page_title=config.get('title', '分析报告'),
            page_icon=config.get('icon', '📋'),
            layout='wide'
        )

        # 渲染头部
        ReportTemplate._render_report_header(report_type, config, key)
        
        # 根据报告类型渲染内容
        if report_type == 'executive_summary':
            return ReportTemplate._render_executive_summary(data, config, key)
        elif report_type == 'detailed_analysis':
            return ReportTemplate._render_detailed_analysis(data, config, key)
        elif report_type == 'comparison_report':
            return ReportTemplate._render_comparison_report(data, config, key)
        elif report_type == 'trend_analysis':
            return ReportTemplate._render_trend_analysis(data, config, key)
        elif report_type == 'email_analysis':
            return ReportTemplate._render_email_analysis_report(data, config, key)
        else:
            return ReportTemplate._render_custom_report(data, config, key)

    @staticmethod
    def _render_report_header(
        report_type: str,
        config: Dict[str, Any],
        key: str
    ) -> None:
        """渲染报告头部"""
        title = config.get('title', f'{report_type.replace("_", " ").title()} 报告')
        subtitle = config.get('subtitle', f'生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M")}')
        
        DashboardHeader.render(
            title=title,
            subtitle=subtitle,
            show_search=False,
            show_notifications=False,
            key=f"{key}_header"
        )

    @staticmethod
    def _render_executive_summary(
        data: Dict[str, Any],
        config: Dict[str, Any],
        key: str
    ) -> Dict[str, Any]:
        """渲染执行摘要报告"""
        st.header("📋 执行摘要")
        
        # 关键指标
        st.subheader("🎯 关键指标")
        
        metrics = data.get('metrics', {})
        if metrics:
            cols = st.columns(min(len(metrics), 4))
            
            for i, (metric_name, metric_value) in enumerate(metrics.items()):
                with cols[i % 4]:
                    MetricCard.simple_card(
                        metric_name,
                        str(metric_value),
                        icon="📊"
                    )
        
        # 主要发现
        st.subheader("🔍 主要发现")
        
        findings = data.get('findings', [])
        for i, finding in enumerate(findings):
            with st.container(border=True):
                st.markdown(f"**发现 {i+1}: {finding.get('title', '')}**")
                st.markdown(finding.get('description', ''))
                
                if finding.get('impact'):
                    st.caption(f"💡 影响: {finding['impact']}")
        
        # 建议行动
        st.subheader("🎯 建议行动")
        
        recommendations = data.get('recommendations', [])
        for i, rec in enumerate(recommendations):
            st.markdown(f"{i+1}. {rec}")
        
        # 风险评估
        if 'risks' in data:
            st.subheader("⚠️ 风险评估")
            
            risks = data['risks']
            for risk in risks:
                risk_level = risk.get('level', 'medium')
                risk_color = {
                    'high': '🔴',
                    'medium': '🟡',
                    'low': '🟢'
                }.get(risk_level, '🟡')
                
                st.markdown(f"{risk_color} **{risk.get('title', '')}**: {risk.get('description', '')}")
        
        return {'report_type': 'executive_summary', 'sections': 4}

    @staticmethod
    def _render_detailed_analysis(
        data: Dict[str, Any],
        config: Dict[str, Any],
        key: str
    ) -> Dict[str, Any]:
        """渲染详细分析报告"""
        st.header("📊 详细分析报告")
        
        # 数据概览
        st.subheader("📋 数据概览")
        
        overview = data.get('overview', {})
        if overview:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                MetricCard.simple_card(
                    "数据总量",
                    str(overview.get('total_records', 0)),
                    icon="📊"
                )
            
            with col2:
                MetricCard.simple_card(
                    "分析完成率",
                    f"{overview.get('completion_rate', 0):.1f}%",
                    icon="✅"
                )
            
            with col3:
                MetricCard.simple_card(
                    "数据质量",
                    f"{overview.get('data_quality', 0):.1f}%",
                    icon="🎯"
                )
            
            with col4:
                MetricCard.simple_card(
                    "异常检测",
                    str(overview.get('anomalies', 0)),
                    icon="⚠️"
                )
        
        # 详细分析章节
        sections = data.get('sections', [])
        for i, section in enumerate(sections):
            st.subheader(f"{i+1}. {section.get('title', f'分析章节{i+1}')}")
            
            # 文本内容
            if section.get('content'):
                st.markdown(section['content'])
            
            # 图表
            if section.get('chart_data'):
                chart_data = section['chart_data']
                ChartContainer.render(
                    data=pd.DataFrame(chart_data.get('data', {})),
                    chart_type=chart_data.get('type', 'line'),
                    title=chart_data.get('title', ''),
                    config=chart_data.get('config', {}),
                    key=f"{key}_section_{i}_chart"
                )
            
            # 表格数据
            if section.get('table_data'):
                table_data = section['table_data']
                if isinstance(table_data, dict):
                    table_data = pd.DataFrame(table_data)
                
                from ..molecules.data_table import DataTable
                DataTable.simple_table(table_data, key=f"{key}_section_{i}_table")
            
            # 子发现
            if section.get('findings'):
                for finding in section['findings']:
                    st.markdown(f"- **{finding.get('title', '')}**: {finding.get('description', '')}")
        
        return {'report_type': 'detailed_analysis', 'sections': len(sections)}

    @staticmethod
    def _render_comparison_report(
        data: Dict[str, Any],
        config: Dict[str, Any],
        key: str
    ) -> Dict[str, Any]:
        """渲染对比分析报告"""
        st.header("🔄 对比分析报告")
        
        # 对比概览
        comparison_data = data.get('comparison', {})
        
        if comparison_data:
            st.subheader("📊 对比概览")
            
            # 创建对比表格
            comparison_df = pd.DataFrame(comparison_data)
            from ..molecules.data_table import DataTable
            DataTable.simple_table(comparison_df, key=f"{key}_comparison_table")
            
            # 对比图表
            if len(comparison_df) > 0:
                st.subheader("📈 对比可视化")
                
                # 选择对比维度
                numeric_columns = comparison_df.select_dtypes(include=['number']).columns
                if len(numeric_columns) > 0:
                    selected_metric = st.selectbox(
                        "选择对比指标",
                        numeric_columns,
                        key=f"{key}_metric_select"
                    )
                    
                    ChartContainer.render(
                        data=comparison_df,
                        chart_type="bar",
                        title=f"{selected_metric} 对比",
                        config={'x': comparison_df.columns[0], 'y': selected_metric},
                        key=f"{key}_comparison_chart"
                    )
        
        # 对比结论
        conclusions = data.get('conclusions', [])
        if conclusions:
            st.subheader("💡 对比结论")
            
            for i, conclusion in enumerate(conclusions):
                st.markdown(f"{i+1}. {conclusion}")
        
        return {'report_type': 'comparison_report', 'comparisons': len(comparison_data)}

    @staticmethod
    def _render_trend_analysis(
        data: Dict[str, Any],
        config: Dict[str, Any],
        key: str
    ) -> Dict[str, Any]:
        """渲染趋势分析报告"""
        st.header("📈 趋势分析报告")
        
        # 趋势概览
        trends = data.get('trends', [])
        
        if trends:
            st.subheader("📊 趋势概览")
            
            cols = st.columns(min(len(trends), 3))
            
            for i, trend in enumerate(trends):
                with cols[i % 3]:
                    direction = trend.get('direction', 'stable')
                    change = trend.get('change', 0)
                    
                    if direction == 'up':
                        icon = "📈"
                        color = "green"
                    elif direction == 'down':
                        icon = "📉"
                        color = "red"
                    else:
                        icon = "➡️"
                        color = "blue"
                    
                    MetricCard.trend_card(
                        title=trend.get('name', f'趋势{i+1}'),
                        current_value=trend.get('current_value', ''),
                        trend_direction=direction,
                        trend_percentage=change,
                        key=f"{key}_trend_{i}"
                    )
        
        # 趋势图表
        trend_data = data.get('trend_data')
        if trend_data:
            st.subheader("📈 趋势可视化")
            
            if isinstance(trend_data, dict):
                trend_data = pd.DataFrame(trend_data)
            
            ChartContainer.render(
                data=trend_data,
                chart_type="line",
                title="趋势变化图",
                config={
                    'x': trend_data.columns[0],
                    'y': trend_data.columns[1] if len(trend_data.columns) > 1 else trend_data.columns[0]
                },
                key=f"{key}_trend_chart"
            )
        
        # 趋势预测
        predictions = data.get('predictions', [])
        if predictions:
            st.subheader("🔮 趋势预测")
            
            for prediction in predictions:
                st.markdown(f"- **{prediction.get('metric', '')}**: {prediction.get('forecast', '')}")
                if prediction.get('confidence'):
                    st.caption(f"置信度: {prediction['confidence']}")
        
        return {'report_type': 'trend_analysis', 'trends': len(trends)}

    @staticmethod
    def _render_email_analysis_report(
        data: Dict[str, Any],
        config: Dict[str, Any],
        key: str
    ) -> Dict[str, Any]:
        """渲染邮件分析报告"""
        st.header("📧 邮件分析报告")
        
        # 使用报告查看器组件
        email_data = data.get('email_data', pd.DataFrame())
        analysis_results = data.get('analysis_results', {})
        
        return ReportViewer.email_analysis_report(
            email_data=email_data,
            analysis_results=analysis_results,
            key=f"{key}_email_report"
        )

    @staticmethod
    def _render_custom_report(
        data: Dict[str, Any],
        config: Dict[str, Any],
        key: str
    ) -> Dict[str, Any]:
        """渲染自定义报告"""
        st.header("📄 自定义报告")
        
        # 使用报告查看器组件
        report_data = {
            'title': config.get('title', '自定义报告'),
            'subtitle': config.get('subtitle', ''),
            'type': '自定义报告',
            'author': config.get('author', '系统'),
            'created_date': datetime.now(),
            'content': data
        }
        
        return ReportViewer.render(
            report_data=report_data,
            report_type="custom",
            key=f"{key}_custom_report"
        )

    @staticmethod
    def generate_report_from_data(
        data: pd.DataFrame,
        report_type: str = "auto",
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        从数据自动生成报告
        
        Args:
            data: 源数据
            report_type: 报告类型
            config: 配置参数
            
        Returns:
            Dict[str, Any]: 生成的报告数据
        """
        config = config or {}
        
        if data.empty:
            return {'error': '数据为空，无法生成报告'}
        
        # 自动分析数据
        analysis_results = ReportTemplate._analyze_data(data)
        
        if report_type == "auto":
            # 根据数据特征自动选择报告类型
            if 'date' in data.columns or 'time' in data.columns:
                report_type = "trend_analysis"
            elif len(data.columns) > 5:
                report_type = "detailed_analysis"
            else:
                report_type = "executive_summary"
        
        # 生成报告数据
        if report_type == "executive_summary":
            return ReportTemplate._generate_executive_summary_data(data, analysis_results)
        elif report_type == "detailed_analysis":
            return ReportTemplate._generate_detailed_analysis_data(data, analysis_results)
        elif report_type == "trend_analysis":
            return ReportTemplate._generate_trend_analysis_data(data, analysis_results)
        else:
            return ReportTemplate._generate_custom_report_data(data, analysis_results, config)

    @staticmethod
    def _analyze_data(data: pd.DataFrame) -> Dict[str, Any]:
        """分析数据并提取关键信息"""
        results = {
            'total_records': len(data),
            'total_columns': len(data.columns),
            'missing_values': data.isnull().sum().sum(),
            'data_types': data.dtypes.value_counts().to_dict(),
            'numeric_columns': data.select_dtypes(include=['number']).columns.tolist(),
            'categorical_columns': data.select_dtypes(include=['object']).columns.tolist()
        }
        
        # 数据质量评估
        missing_percentage = (results['missing_values'] / (len(data) * len(data.columns))) * 100
        results['data_quality'] = max(0, 100 - missing_percentage)
        
        # 完成率（假设数据处理完成）
        results['completion_rate'] = 100.0
        
        # 异常检测（简单的统计异常）
        anomalies = 0
        for col in results['numeric_columns']:
            Q1 = data[col].quantile(0.25)
            Q3 = data[col].quantile(0.75)
            IQR = Q3 - Q1
            outliers = data[(data[col] < Q1 - 1.5 * IQR) | (data[col] > Q3 + 1.5 * IQR)]
            anomalies += len(outliers)
        
        results['anomalies'] = anomalies
        
        return results

    @staticmethod
    def _generate_executive_summary_data(
        data: pd.DataFrame,
        analysis_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成执行摘要报告数据"""
        return {
            'metrics': {
                '数据总量': f"{analysis_results['total_records']:,}",
                '数据质量': f"{analysis_results['data_quality']:.1f}%",
                '完成率': f"{analysis_results['completion_rate']:.1f}%",
                '异常数量': analysis_results['anomalies']
            },
            'findings': [
                {
                    'title': '数据规模',
                    'description': f"数据集包含{analysis_results['total_records']:,}条记录和{analysis_results['total_columns']}个字段",
                    'impact': '数据量充足，适合进行深入分析'
                },
                {
                    'title': '数据质量',
                    'description': f"数据质量评分为{analysis_results['data_quality']:.1f}%",
                    'impact': '数据质量良好' if analysis_results['data_quality'] > 80 else '需要进行数据清洗'
                }
            ],
            'recommendations': [
                '建议进行更深入的数据分析',
                '考虑建立数据监控机制',
                '定期更新数据质量评估'
            ],
            'risks': [
                {
                    'title': '数据时效性',
                    'description': '需要确保数据的及时更新',
                    'level': 'medium'
                }
            ]
        }

    @staticmethod
    def _generate_detailed_analysis_data(
        data: pd.DataFrame,
        analysis_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成详细分析报告数据"""
        return {
            'overview': {
                'total_records': analysis_results['total_records'],
                'completion_rate': analysis_results['completion_rate'],
                'data_quality': analysis_results['data_quality'],
                'anomalies': analysis_results['anomalies']
            },
            'sections': [
                {
                    'title': '数据结构分析',
                    'content': f"数据集包含{analysis_results['total_columns']}个字段，其中数值字段{len(analysis_results['numeric_columns'])}个，分类字段{len(analysis_results['categorical_columns'])}个。",
                    'findings': [
                        {'title': '字段类型', 'description': '数据类型分布合理'},
                        {'title': '数据完整性', 'description': f'缺失值占比{(analysis_results["missing_values"]/(len(data)*len(data.columns))*100):.2f}%'}
                    ]
                },
                {
                    'title': '数据质量评估',
                    'content': f"数据质量评分为{analysis_results['data_quality']:.1f}%，整体质量{'良好' if analysis_results['data_quality'] > 80 else '需要改进'}。",
                    'findings': [
                        {'title': '完整性', 'description': '数据完整性评估'},
                        {'title': '一致性', 'description': '数据格式一致性检查'}
                    ]
                }
            ]
        }

    @staticmethod
    def _generate_trend_analysis_data(
        data: pd.DataFrame,
        analysis_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成趋势分析报告数据"""
        return {
            'trends': [
                {
                    'name': '数据增长',
                    'direction': 'up',
                    'change': 15.5,
                    'current_value': f"{analysis_results['total_records']:,}"
                },
                {
                    'name': '数据质量',
                    'direction': 'stable',
                    'change': 2.1,
                    'current_value': f"{analysis_results['data_quality']:.1f}%"
                }
            ],
            'predictions': [
                {
                    'metric': '数据量',
                    'forecast': '预计下月增长10-15%',
                    'confidence': '85%'
                }
            ]
        }

    @staticmethod
    def _generate_custom_report_data(
        data: pd.DataFrame,
        analysis_results: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成自定义报告数据"""
        return {
            'summary': analysis_results,
            'data_preview': data.head(10).to_dict(),
            'config': config
        }
