#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置驱动渲染器

模块描述: 通过配置文件动态生成页面和组件，支持JSON/YAML配置
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..organisms, ..molecules, ..atoms, streamlit, typing
"""

import streamlit as st
import pandas as pd
import json
import yaml
from typing import Dict, Any, List, Optional, Union
from ..organisms.analysis_panel import AnalysisPanel
from ..organisms.dashboard_header import DashboardHeader
from ..organisms.navigation_sidebar import NavigationSidebar
from ..organisms.report_viewer import ReportViewer
from ..molecules.metric_card import MetricCard
from ..molecules.chart_container import ChartContainer
from ..molecules.data_table import DataTable
from ..molecules.search_box import SearchBox
from ..molecules.filter_panel import FilterPanel

class ConfigDrivenRenderer:
    """配置驱动渲染器 - 通过配置文件动态生成界面"""

    @staticmethod
    def render_from_config(
        config: Union[Dict[str, Any], str],
        data: Dict[str, pd.DataFrame] = None,
        key: str = "config_driven"
    ) -> Dict[str, Any]:
        """
        从配置渲染界面
        
        Args:
            config: 配置字典或配置文件路径
            data: 数据字典
            key: 组件唯一标识
            
        Returns:
            Dict[str, Any]: 渲染结果
        """
        # 解析配置
        if isinstance(config, str):
            config = ConfigDrivenRenderer._load_config_file(config)
        
        data = data or {}
        
        # 页面配置
        page_config = config.get('page', {})
        if page_config:
            st.set_page_config(
                page_title=page_config.get('title', 'Streamlit App'),
                page_icon=page_config.get('icon', '📊'),
                layout=page_config.get('layout', 'wide'),
                initial_sidebar_state=page_config.get('sidebar_state', 'expanded')
            )
        
        # 渲染组件
        components = config.get('components', [])
        results = {}
        
        for i, component_config in enumerate(components):
            component_key = f"{key}_component_{i}"
            component_result = ConfigDrivenRenderer._render_component(
                component_config, data, component_key
            )
            
            component_name = component_config.get('name', f'component_{i}')
            results[component_name] = component_result
        
        return results

    @staticmethod
    def _load_config_file(file_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.json'):
                    return json.load(f)
                elif file_path.endswith(('.yml', '.yaml')):
                    return yaml.safe_load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {file_path}")
        except Exception as e:
            st.error(f"加载配置文件失败: {e}")
            return {}

    @staticmethod
    def _render_component(
        component_config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Any:
        """渲染单个组件"""
        component_type = component_config.get('type', '')
        
        # 容器组件
        if component_type == 'container':
            return ConfigDrivenRenderer._render_container(component_config, data, key)
        elif component_type == 'columns':
            return ConfigDrivenRenderer._render_columns(component_config, data, key)
        elif component_type == 'tabs':
            return ConfigDrivenRenderer._render_tabs(component_config, data, key)
        elif component_type == 'expander':
            return ConfigDrivenRenderer._render_expander(component_config, data, key)
        
        # 原子组件
        elif component_type == 'text':
            return ConfigDrivenRenderer._render_text(component_config, key)
        elif component_type == 'button':
            return ConfigDrivenRenderer._render_button(component_config, key)
        elif component_type == 'input':
            return ConfigDrivenRenderer._render_input(component_config, key)
        
        # 分子组件
        elif component_type == 'metric_card':
            return ConfigDrivenRenderer._render_metric_card(component_config, data, key)
        elif component_type == 'chart':
            return ConfigDrivenRenderer._render_chart(component_config, data, key)
        elif component_type == 'table':
            return ConfigDrivenRenderer._render_table(component_config, data, key)
        elif component_type == 'search_box':
            return ConfigDrivenRenderer._render_search_box(component_config, key)
        elif component_type == 'filter_panel':
            return ConfigDrivenRenderer._render_filter_panel(component_config, key)
        
        # 有机体组件
        elif component_type == 'analysis_panel':
            return ConfigDrivenRenderer._render_analysis_panel(component_config, data, key)
        elif component_type == 'dashboard_header':
            return ConfigDrivenRenderer._render_dashboard_header(component_config, key)
        elif component_type == 'navigation_sidebar':
            return ConfigDrivenRenderer._render_navigation_sidebar(component_config, key)
        elif component_type == 'report_viewer':
            return ConfigDrivenRenderer._render_report_viewer(component_config, data, key)
        
        else:
            st.warning(f"未知组件类型: {component_type}")
            return None

    @staticmethod
    def _render_container(
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染容器"""
        border = config.get('border', False)
        
        with st.container(border=border):
            # 渲染标题
            if config.get('title'):
                st.subheader(config['title'])
            
            # 渲染子组件
            children = config.get('children', [])
            results = {}
            
            for i, child_config in enumerate(children):
                child_key = f"{key}_child_{i}"
                child_result = ConfigDrivenRenderer._render_component(
                    child_config, data, child_key
                )
                
                child_name = child_config.get('name', f'child_{i}')
                results[child_name] = child_result
            
            return results

    @staticmethod
    def _render_columns(
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染列布局"""
        column_specs = config.get('specs', [1, 1])
        gap = config.get('gap', 'small')
        
        cols = st.columns(column_specs, gap=gap)
        
        children = config.get('children', [])
        results = {}
        
        for i, (col, child_config) in enumerate(zip(cols, children)):
            with col:
                child_key = f"{key}_col_{i}"
                child_result = ConfigDrivenRenderer._render_component(
                    child_config, data, child_key
                )
                
                child_name = child_config.get('name', f'col_{i}')
                results[child_name] = child_result
        
        return results

    @staticmethod
    def _render_tabs(
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染标签页"""
        tab_configs = config.get('tabs', [])
        tab_names = [tab.get('name', f'标签{i+1}') for i, tab in enumerate(tab_configs)]
        
        tabs = st.tabs(tab_names)
        results = {}
        
        for i, (tab, tab_config) in enumerate(zip(tabs, tab_configs)):
            with tab:
                tab_key = f"{key}_tab_{i}"
                
                # 渲染标签页内容
                tab_children = tab_config.get('children', [])
                tab_results = {}
                
                for j, child_config in enumerate(tab_children):
                    child_key = f"{tab_key}_child_{j}"
                    child_result = ConfigDrivenRenderer._render_component(
                        child_config, data, child_key
                    )
                    
                    child_name = child_config.get('name', f'child_{j}')
                    tab_results[child_name] = child_result
                
                results[tab_config.get('name', f'tab_{i}')] = tab_results
        
        return results

    @staticmethod
    def _render_expander(
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染可展开容器"""
        label = config.get('label', '展开')
        expanded = config.get('expanded', False)
        
        with st.expander(label, expanded=expanded):
            children = config.get('children', [])
            results = {}
            
            for i, child_config in enumerate(children):
                child_key = f"{key}_child_{i}"
                child_result = ConfigDrivenRenderer._render_component(
                    child_config, data, child_key
                )
                
                child_name = child_config.get('name', f'child_{i}')
                results[child_name] = child_result
            
            return results

    @staticmethod
    def _render_text(config: Dict[str, Any], key: str) -> None:
        """渲染文本组件"""
        text_type = config.get('text_type', 'markdown')
        content = config.get('content', '')
        
        if text_type == 'title':
            st.title(content)
        elif text_type == 'header':
            st.header(content)
        elif text_type == 'subheader':
            st.subheader(content)
        elif text_type == 'caption':
            st.caption(content)
        elif text_type == 'code':
            language = config.get('language', 'python')
            st.code(content, language=language)
        else:
            st.markdown(content)

    @staticmethod
    def _render_button(config: Dict[str, Any], key: str) -> bool:
        """渲染按钮组件"""
        from ..atoms.button import Button, ButtonType
        
        label = config.get('label', '按钮')
        button_type = config.get('button_type', 'primary')
        disabled = config.get('disabled', False)
        use_container_width = config.get('use_container_width', False)
        
        # 转换按钮类型
        if button_type == 'primary':
            return Button.primary(label, key=key, disabled=disabled, use_container_width=use_container_width)
        else:
            return Button.secondary(label, key=key, disabled=disabled, use_container_width=use_container_width)

    @staticmethod
    def _render_input(config: Dict[str, Any], key: str) -> Any:
        """渲染输入组件"""
        from ..atoms.input import Input
        
        input_type = config.get('input_type', 'text')
        label = config.get('label', '输入')
        
        if input_type == 'text':
            return Input.text(
                label,
                value=config.get('value', ''),
                placeholder=config.get('placeholder', ''),
                key=key
            )
        elif input_type == 'number':
            return Input.number(
                label,
                value=config.get('value', 0),
                min_value=config.get('min_value'),
                max_value=config.get('max_value'),
                key=key
            )
        elif input_type == 'selectbox':
            return Input.selectbox(
                label,
                options=config.get('options', []),
                key=key
            )
        elif input_type == 'multiselect':
            return Input.multiselect(
                label,
                options=config.get('options', []),
                default=config.get('default', []),
                key=key
            )
        elif input_type == 'checkbox':
            return Input.checkbox(
                label,
                value=config.get('value', False),
                key=key
            )
        elif input_type == 'slider':
            return Input.slider(
                label,
                min_value=config.get('min_value', 0),
                max_value=config.get('max_value', 100),
                value=config.get('value', 50),
                key=key
            )
        else:
            return Input.text(label, key=key)

    @staticmethod
    def _render_metric_card(
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> None:
        """渲染指标卡片"""
        title = config.get('title', '指标')
        value = config.get('value', '0')
        icon = config.get('icon', '📊')
        
        # 如果值是数据引用，从数据中获取
        if isinstance(value, str) and value.startswith('data.'):
            data_path = value.split('.')
            if len(data_path) >= 2 and data_path[1] in data:
                df = data[data_path[1]]
                if len(data_path) >= 3 and data_path[2] in df.columns:
                    value = str(df[data_path[2]].iloc[0] if len(df) > 0 else 0)
        
        MetricCard.simple_card(title, str(value), icon=icon)

    @staticmethod
    def _render_chart(
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> None:
        """渲染图表"""
        data_source = config.get('data_source', '')
        chart_type = config.get('chart_type', 'line')
        title = config.get('title', '图表')
        chart_config = config.get('config', {})
        
        if data_source in data:
            ChartContainer.render(
                data=data[data_source],
                chart_type=chart_type,
                title=title,
                config=chart_config,
                key=key
            )
        else:
            st.warning(f"数据源 '{data_source}' 不存在")

    @staticmethod
    def _render_table(
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染表格"""
        data_source = config.get('data_source', '')
        
        if data_source in data:
            return DataTable.render(
                data=data[data_source],
                sortable=config.get('sortable', True),
                filterable=config.get('filterable', True),
                paginated=config.get('paginated', True),
                page_size=config.get('page_size', 20),
                key=key
            )
        else:
            st.warning(f"数据源 '{data_source}' 不存在")
            return {}

    @staticmethod
    def _render_search_box(config: Dict[str, Any], key: str) -> tuple:
        """渲染搜索框"""
        placeholder = config.get('placeholder', '搜索...')
        
        return SearchBox.render(
            placeholder=placeholder,
            key=key
        )

    @staticmethod
    def _render_filter_panel(config: Dict[str, Any], key: str) -> Dict[str, Any]:
        """渲染筛选面板"""
        filters = config.get('filters', {})
        title = config.get('title', '筛选条件')
        
        return FilterPanel.render(
            filters=filters,
            title=title,
            key=key
        )

    @staticmethod
    def _render_analysis_panel(
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染分析面板"""
        data_source = config.get('data_source', '')
        title = config.get('title', '数据分析')
        panel_config = config.get('config', {})
        
        if data_source in data:
            return AnalysisPanel.render(
                data=data[data_source],
                title=title,
                config=panel_config,
                key=key
            )
        else:
            st.warning(f"数据源 '{data_source}' 不存在")
            return {}

    @staticmethod
    def _render_dashboard_header(config: Dict[str, Any], key: str) -> Dict[str, Any]:
        """渲染仪表盘头部"""
        return DashboardHeader.render(
            title=config.get('title', '仪表盘'),
            subtitle=config.get('subtitle'),
            user_info=config.get('user_info'),
            key=key
        )

    @staticmethod
    def _render_navigation_sidebar(config: Dict[str, Any], key: str) -> Dict[str, Any]:
        """渲染导航侧边栏"""
        navigation_config = config.get('navigation', NavigationSidebar.email_navigation())
        
        return NavigationSidebar.render(
            navigation_config=navigation_config,
            key=key
        )

    @staticmethod
    def _render_report_viewer(
        config: Dict[str, Any],
        data: Dict[str, pd.DataFrame],
        key: str
    ) -> Dict[str, Any]:
        """渲染报告查看器"""
        report_data = config.get('report_data', {})
        report_type = config.get('report_type', 'analysis')
        
        return ReportViewer.render(
            report_data=report_data,
            report_type=report_type,
            key=key
        )

    @staticmethod
    def create_sample_config() -> Dict[str, Any]:
        """创建示例配置"""
        return {
            "page": {
                "title": "配置驱动演示",
                "icon": "🔧",
                "layout": "wide"
            },
            "components": [
                {
                    "type": "dashboard_header",
                    "name": "header",
                    "title": "配置驱动仪表盘",
                    "subtitle": "通过配置文件生成的界面"
                },
                {
                    "type": "columns",
                    "name": "metrics_row",
                    "specs": [1, 1, 1, 1],
                    "children": [
                        {
                            "type": "metric_card",
                            "name": "metric1",
                            "title": "总数据量",
                            "value": "1,234",
                            "icon": "📊"
                        },
                        {
                            "type": "metric_card",
                            "name": "metric2",
                            "title": "处理完成",
                            "value": "95.6%",
                            "icon": "✅"
                        },
                        {
                            "type": "metric_card",
                            "name": "metric3",
                            "title": "准确率",
                            "value": "87.3%",
                            "icon": "🎯"
                        },
                        {
                            "type": "metric_card",
                            "name": "metric4",
                            "title": "异常检测",
                            "value": "12",
                            "icon": "⚠️"
                        }
                    ]
                },
                {
                    "type": "tabs",
                    "name": "main_tabs",
                    "tabs": [
                        {
                            "name": "数据分析",
                            "children": [
                                {
                                    "type": "analysis_panel",
                                    "name": "analysis",
                                    "data_source": "sample_data",
                                    "title": "数据分析面板"
                                }
                            ]
                        },
                        {
                            "name": "图表展示",
                            "children": [
                                {
                                    "type": "chart",
                                    "name": "chart1",
                                    "data_source": "sample_data",
                                    "chart_type": "line",
                                    "title": "趋势图表"
                                }
                            ]
                        }
                    ]
                }
            ]
        }

    @staticmethod
    def save_config_to_file(config: Dict[str, Any], file_path: str) -> None:
        """保存配置到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                if file_path.endswith('.json'):
                    json.dump(config, f, ensure_ascii=False, indent=2)
                elif file_path.endswith(('.yml', '.yaml')):
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
                else:
                    raise ValueError(f"不支持的文件格式: {file_path}")
            
            st.success(f"配置已保存到: {file_path}")
        except Exception as e:
            st.error(f"保存配置文件失败: {e}")
