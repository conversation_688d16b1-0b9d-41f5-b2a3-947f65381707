#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析面板有机体组件

模块描述: 复杂的数据分析界面，集成搜索、筛选、图表、表格等功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..molecules, ..atoms, streamlit, pandas, typing
"""

import streamlit as st
import pandas as pd
from typing import Dict, Any, List, Optional, Callable
from ..atoms.text import Text
from ..atoms.button import Button
from ..atoms.icon import Icon
from ..molecules.search_box import SearchBox
from ..molecules.filter_panel import FilterPanel
from ..molecules.metric_card import MetricCard
from ..molecules.data_table import DataTable
from ..molecules.chart_container import ChartContainer

class AnalysisPanel:
    """分析面板有机体组件 - 完整的数据分析界面"""

    @staticmethod
    def render(
        data: pd.DataFrame,
        title: str = "数据分析面板",
        config: Dict[str, Any] = None,
        key: str = "analysis_panel"
    ) -> Dict[str, Any]:
        """
        渲染分析面板
        
        Args:
            data: 分析数据
            title: 面板标题
            config: 配置参数
            key: 组件唯一标识
            
        Returns:
            Dict[str, Any]: 分析结果和用户交互数据
        """
        config = config or {}
        result = {
            'filtered_data': data,
            'selected_rows': [],
            'search_term': '',
            'filter_values': {},
            'chart_config': {}
        }

        # 面板头部
        AnalysisPanel._render_header(title, key)
        
        # 控制面板
        search_term, filter_values = AnalysisPanel._render_controls(
            data, config.get('filters', {}), key
        )
        result['search_term'] = search_term
        result['filter_values'] = filter_values
        
        # 应用搜索和筛选
        filtered_data = AnalysisPanel._apply_filters(data, search_term, filter_values)
        result['filtered_data'] = filtered_data
        
        # 指标概览
        AnalysisPanel._render_metrics(filtered_data, config.get('metrics', {}), key)
        
        # 图表分析
        AnalysisPanel._render_charts(filtered_data, config.get('charts', []), key)
        
        # 数据表格
        table_result = AnalysisPanel._render_data_table(filtered_data, config.get('table', {}), key)
        result['selected_rows'] = table_result.get('selected_rows', [])
        
        # 分析操作
        AnalysisPanel._render_actions(filtered_data, key)
        
        return result

    @staticmethod
    def _render_header(title: str, key: str) -> None:
        """渲染面板头部"""
        col1, col2, col3 = st.columns([6, 2, 2])
        
        with col1:
            Text.header(title, divider=True)
        
        with col2:
            if Button.secondary("刷新数据", key=f"{key}_refresh"):
                st.rerun()
        
        with col3:
            if Button.primary("导出分析", key=f"{key}_export"):
                Text.success("导出功能开发中...")

    @staticmethod
    def _render_controls(
        data: pd.DataFrame, 
        filter_config: Dict[str, Any], 
        key: str
    ) -> tuple:
        """渲染控制面板"""
        Text.subheader("🔍 数据筛选与搜索")
        
        col1, col2 = st.columns([1, 1])
        
        # 搜索框
        with col1:
            search_term, search_clicked, _ = SearchBox.render(
                placeholder="搜索数据...",
                key=f"{key}_search"
            )
        
        # 筛选面板
        with col2:
            if filter_config:
                filter_values = FilterPanel.render(
                    filters=filter_config,
                    title="高级筛选",
                    expanded=False,
                    key=f"{key}_filter"
                )
            else:
                # 自动生成筛选器
                auto_filters = AnalysisPanel._generate_auto_filters(data)
                filter_values = FilterPanel.render(
                    filters=auto_filters,
                    title="自动筛选",
                    expanded=False,
                    key=f"{key}_auto_filter"
                )
        
        return search_term, filter_values

    @staticmethod
    def _generate_auto_filters(data: pd.DataFrame) -> Dict[str, Any]:
        """自动生成筛选器配置"""
        filters = {}
        
        # 为分类列生成选择框
        categorical_columns = data.select_dtypes(include=['object', 'category']).columns
        for col in categorical_columns[:3]:  # 限制最多3个
            unique_values = data[col].dropna().unique()
            if len(unique_values) <= 20:  # 只为选项不太多的列生成筛选器
                filters[col] = {
                    'type': 'multiselect',
                    'label': col,
                    'options': unique_values.tolist(),
                    'default': unique_values.tolist()
                }
        
        # 为数值列生成滑块
        numeric_columns = data.select_dtypes(include=['number']).columns
        for col in numeric_columns[:2]:  # 限制最多2个
            min_val = data[col].min()
            max_val = data[col].max()
            if pd.notna(min_val) and pd.notna(max_val) and min_val != max_val:
                filters[f"{col}_range"] = {
                    'type': 'slider',
                    'label': f"{col} 范围",
                    'min_value': float(min_val),
                    'max_value': float(max_val),
                    'default': float(min_val)
                }
        
        return filters

    @staticmethod
    def _apply_filters(
        data: pd.DataFrame, 
        search_term: str, 
        filter_values: Dict[str, Any]
    ) -> pd.DataFrame:
        """应用搜索和筛选"""
        filtered_data = data.copy()
        
        # 应用搜索
        if search_term:
            # 在所有文本列中搜索
            text_columns = filtered_data.select_dtypes(include=['object']).columns
            if len(text_columns) > 0:
                mask = pd.Series([False] * len(filtered_data))
                for col in text_columns:
                    mask |= filtered_data[col].astype(str).str.contains(
                        search_term, case=False, na=False
                    )
                filtered_data = filtered_data[mask]
        
        # 应用筛选
        for filter_name, filter_value in filter_values.items():
            if filter_value and filter_name in data.columns:
                if isinstance(filter_value, list) and filter_value:
                    filtered_data = filtered_data[filtered_data[filter_name].isin(filter_value)]
                elif isinstance(filter_value, (int, float)):
                    # 处理范围筛选
                    if filter_name.endswith('_range'):
                        base_col = filter_name.replace('_range', '')
                        if base_col in data.columns:
                            filtered_data = filtered_data[filtered_data[base_col] >= filter_value]
        
        return filtered_data

    @staticmethod
    def _render_metrics(
        data: pd.DataFrame, 
        metrics_config: Dict[str, Any], 
        key: str
    ) -> None:
        """渲染指标概览"""
        if data.empty:
            Text.warning("暂无数据显示指标")
            return
        
        Text.subheader("📊 关键指标")
        
        # 自动计算基础指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            MetricCard.simple_card(
                "数据总量", 
                f"{len(data):,}", 
                icon="📋"
            )
        
        with col2:
            # 计算数值列的平均值
            numeric_cols = data.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                avg_value = data[numeric_cols[0]].mean()
                MetricCard.simple_card(
                    f"平均{numeric_cols[0]}", 
                    f"{avg_value:.2f}", 
                    icon="📈"
                )
            else:
                MetricCard.simple_card("数值列", "0", icon="📈")
        
        with col3:
            # 计算分类列的唯一值数量
            categorical_cols = data.select_dtypes(include=['object']).columns
            if len(categorical_cols) > 0:
                unique_count = data[categorical_cols[0]].nunique()
                MetricCard.simple_card(
                    f"唯一{categorical_cols[0]}", 
                    f"{unique_count}", 
                    icon="🏷️"
                )
            else:
                MetricCard.simple_card("分类数", "0", icon="🏷️")
        
        with col4:
            # 数据完整性
            completeness = (1 - data.isnull().sum().sum() / (len(data) * len(data.columns))) * 100
            MetricCard.simple_card(
                "数据完整性", 
                f"{completeness:.1f}%", 
                icon="✅"
            )

    @staticmethod
    def _render_charts(
        data: pd.DataFrame, 
        charts_config: List[Dict[str, Any]], 
        key: str
    ) -> None:
        """渲染图表分析"""
        if data.empty:
            Text.warning("暂无数据显示图表")
            return
        
        Text.subheader("📈 数据可视化")
        
        if charts_config:
            # 使用配置的图表
            for i, chart_config in enumerate(charts_config):
                ChartContainer.render(
                    data=data,
                    chart_type=chart_config.get('type', 'line'),
                    title=chart_config.get('title', f'图表{i+1}'),
                    config=chart_config.get('config', {}),
                    key=f"{key}_chart_{i}"
                )
        else:
            # 自动生成图表
            AnalysisPanel._render_auto_charts(data, key)

    @staticmethod
    def _render_auto_charts(data: pd.DataFrame, key: str) -> None:
        """自动生成图表"""
        numeric_cols = data.select_dtypes(include=['number']).columns
        categorical_cols = data.select_dtypes(include=['object']).columns
        
        if len(numeric_cols) >= 2:
            # 数值列相关性图表
            col1, col2 = st.columns(2)
            
            with col1:
                ChartContainer.render(
                    data=data,
                    chart_type="scatter",
                    title="数值关系分析",
                    config={
                        'x': numeric_cols[0],
                        'y': numeric_cols[1],
                        'color': categorical_cols[0] if len(categorical_cols) > 0 else None
                    },
                    key=f"{key}_auto_scatter"
                )
            
            with col2:
                ChartContainer.render(
                    data=data,
                    chart_type="line",
                    title="趋势分析",
                    config={
                        'x': data.columns[0],
                        'y': numeric_cols[0]
                    },
                    key=f"{key}_auto_line"
                )
        
        elif len(categorical_cols) > 0 and len(numeric_cols) > 0:
            # 分类统计图表
            ChartContainer.render(
                data=data,
                chart_type="bar",
                title="分类统计",
                config={
                    'x': categorical_cols[0],
                    'y': numeric_cols[0]
                },
                key=f"{key}_auto_bar"
            )

    @staticmethod
    def _render_data_table(
        data: pd.DataFrame, 
        table_config: Dict[str, Any], 
        key: str
    ) -> Dict[str, Any]:
        """渲染数据表格"""
        Text.subheader("📋 详细数据")
        
        if data.empty:
            Text.warning("暂无数据显示")
            return {'selected_rows': []}
        
        # 表格配置
        config = {
            'sortable': table_config.get('sortable', True),
            'filterable': table_config.get('filterable', True),
            'paginated': table_config.get('paginated', True),
            'page_size': table_config.get('page_size', 20),
            'selection_mode': table_config.get('selection_mode', 'multiple')
        }
        
        return DataTable.render(
            data=data,
            **config,
            key=f"{key}_table"
        )

    @staticmethod
    def _render_actions(data: pd.DataFrame, key: str) -> None:
        """渲染分析操作"""
        Text.subheader("🛠️ 分析操作")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if Button.primary("生成报告", key=f"{key}_report"):
                Text.success("报告生成功能开发中...")
        
        with col2:
            if Button.secondary("数据导出", key=f"{key}_export_data"):
                # 实现数据导出
                csv_data = data.to_csv(index=False)
                st.download_button(
                    "下载CSV",
                    csv_data,
                    "analysis_data.csv",
                    "text/csv",
                    key=f"{key}_download_csv"
                )
        
        with col3:
            if Button.secondary("保存分析", key=f"{key}_save"):
                Text.info("保存分析功能开发中...")
        
        with col4:
            if Button.secondary("分享结果", key=f"{key}_share"):
                Text.info("分享功能开发中...")

    @staticmethod
    def email_analysis_panel(
        email_data: pd.DataFrame,
        key: str = "email_analysis"
    ) -> Dict[str, Any]:
        """
        邮件分析专用面板
        
        Args:
            email_data: 邮件数据
            key: 组件唯一标识
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        # 邮件分析特定配置
        config = {
            'filters': {
                'sender': {
                    'type': 'multiselect',
                    'label': '发件人',
                    'options': email_data['sender'].unique().tolist() if 'sender' in email_data.columns else [],
                    'default': []
                },
                'email_type': {
                    'type': 'selectbox',
                    'label': '邮件类型',
                    'options': ['全部'] + (email_data['email_type'].unique().tolist() if 'email_type' in email_data.columns else []),
                    'default': '全部'
                },
                'date_range': {
                    'type': 'date',
                    'label': '日期范围'
                }
            },
            'metrics': {
                'total_emails': '邮件总数',
                'unique_senders': '发件人数量',
                'avg_confidence': '平均置信度',
                'analysis_rate': '分析完成率'
            },
            'charts': [
                {
                    'type': 'pie',
                    'title': '邮件类型分布',
                    'config': {
                        'values': 'count',
                        'names': 'email_type'
                    }
                },
                {
                    'type': 'bar',
                    'title': '发件人邮件数量',
                    'config': {
                        'x': 'sender',
                        'y': 'count'
                    }
                }
            ]
        }
        
        return AnalysisPanel.render(
            data=email_data,
            title="📧 邮件分析面板",
            config=config,
            key=key
        )
