#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
图表容器分子组件

模块描述: 提供统一的图表显示容器，支持多种图表类型和配置
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ..atoms, streamlit, plotly, typing
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, Any, List, Optional, Union
from ..atoms.text import Text
from ..atoms.button import Button
from ..atoms.input import Input

class ChartContainer:
    """图表容器分子组件 - 提供统一的图表显示容器"""

    @staticmethod
    def render(
        data: Union[pd.DataFrame, Dict[str, Any]],
        chart_type: str = "line",
        title: str = "",
        config: Dict[str, Any] = None,
        height: int = 400,
        key: str = "chart_container",
        export_options: bool = True
    ) -> None:
        """
        渲染图表容器
        
        Args:
            data: 图表数据
            chart_type: 图表类型
            title: 图表标题
            config: 图表配置
            height: 图表高度
            key: 组件唯一标识
            export_options: 是否显示导出选项
        """
        config = config or {}
        
        # 图表标题
        if title:
            Text.subheader(title)
        
        # 创建图表
        try:
            fig = ChartContainer._create_chart(data, chart_type, config)
            
            if fig:
                # 设置图表高度
                fig.update_layout(height=height)
                
                # 显示图表
                st.plotly_chart(fig, use_container_width=True, key=key)
                
                # 导出选项
                if export_options:
                    ChartContainer._render_export_options(fig, title or "chart", key)
            else:
                Text.error("无法创建图表")
                
        except Exception as e:
            Text.error(f"图表渲染错误: {e}")

    @staticmethod
    def _create_chart(
        data: Union[pd.DataFrame, Dict[str, Any]],
        chart_type: str,
        config: Dict[str, Any]
    ):
        """创建图表"""
        if isinstance(data, dict):
            # 如果是字典，转换为DataFrame
            data = pd.DataFrame(data)
        
        if data.empty:
            Text.warning("数据为空，无法创建图表")
            return None
        
        # 获取配置参数
        x = config.get('x', data.columns[0] if len(data.columns) > 0 else None)
        y = config.get('y', data.columns[1] if len(data.columns) > 1 else None)
        color = config.get('color')
        size = config.get('size')
        
        # 根据图表类型创建图表
        if chart_type == "line":
            return px.line(data, x=x, y=y, color=color, title=config.get('title', ''))
        
        elif chart_type == "bar":
            return px.bar(data, x=x, y=y, color=color, title=config.get('title', ''))
        
        elif chart_type == "scatter":
            return px.scatter(data, x=x, y=y, color=color, size=size, title=config.get('title', ''))
        
        elif chart_type == "pie":
            values = config.get('values', y)
            names = config.get('names', x)
            return px.pie(data, values=values, names=names, title=config.get('title', ''))
        
        elif chart_type == "histogram":
            return px.histogram(data, x=x, color=color, title=config.get('title', ''))
        
        elif chart_type == "box":
            return px.box(data, x=x, y=y, color=color, title=config.get('title', ''))
        
        elif chart_type == "heatmap":
            # 创建热力图
            if isinstance(data, pd.DataFrame):
                # 如果是DataFrame，使用数值列创建相关性热力图
                numeric_data = data.select_dtypes(include=['number'])
                if not numeric_data.empty:
                    corr_matrix = numeric_data.corr()
                    return px.imshow(corr_matrix, text_auto=True, title=config.get('title', ''))
        
        elif chart_type == "area":
            return px.area(data, x=x, y=y, color=color, title=config.get('title', ''))
        
        elif chart_type == "funnel":
            return px.funnel(data, x=x, y=y, title=config.get('title', ''))
        
        else:
            # 默认使用线图
            return px.line(data, x=x, y=y, color=color, title=config.get('title', ''))

    @staticmethod
    def _render_export_options(fig, filename: str, key: str) -> None:
        """渲染导出选项"""
        with st.expander("导出选项", expanded=False):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if Button.secondary("下载 PNG", key=f"{key}_png"):
                    # 注意：实际导出需要kaleido库
                    Text.info("PNG导出功能需要安装 kaleido 库")
            
            with col2:
                if Button.secondary("下载 HTML", key=f"{key}_html"):
                    html_str = fig.to_html()
                    st.download_button(
                        "下载 HTML",
                        html_str,
                        f"{filename}.html",
                        "text/html",
                        key=f"{key}_html_download"
                    )
            
            with col3:
                if Button.secondary("下载 JSON", key=f"{key}_json"):
                    json_str = fig.to_json()
                    st.download_button(
                        "下载 JSON",
                        json_str,
                        f"{filename}.json",
                        "application/json",
                        key=f"{key}_json_download"
                    )

    @staticmethod
    def interactive_chart(
        data: pd.DataFrame,
        key: str = "interactive_chart"
    ) -> None:
        """交互式图表配置"""
        with st.sidebar:
            Text.subheader("图表配置")
            
            # 图表类型选择
            chart_type = Input.selectbox(
                "图表类型",
                ["line", "bar", "scatter", "pie", "histogram", "box", "area"],
                key=f"{key}_type"
            )
            
            # 数据列选择
            if not data.empty:
                x_column = Input.selectbox(
                    "X轴",
                    data.columns.tolist(),
                    key=f"{key}_x"
                )
                
                y_column = Input.selectbox(
                    "Y轴",
                    data.columns.tolist(),
                    index=1 if len(data.columns) > 1 else 0,
                    key=f"{key}_y"
                )
                
                color_column = Input.selectbox(
                    "颜色分组",
                    ["无"] + data.columns.tolist(),
                    key=f"{key}_color"
                )
                
                # 图表标题
                title = Input.text(
                    "图表标题",
                    key=f"{key}_title"
                )
                
                # 创建配置
                config = {
                    'x': x_column,
                    'y': y_column,
                    'title': title
                }
                
                if color_column != "无":
                    config['color'] = color_column
                
                # 渲染图表
                ChartContainer.render(
                    data=data,
                    chart_type=chart_type,
                    title=title,
                    config=config,
                    key=f"{key}_main"
                )

    @staticmethod
    def multi_chart_container(
        charts: List[Dict[str, Any]],
        layout: str = "columns",  # columns, rows, tabs
        key: str = "multi_chart"
    ) -> None:
        """多图表容器"""
        if layout == "columns":
            cols = st.columns(len(charts))
            for i, chart_config in enumerate(charts):
                with cols[i]:
                    ChartContainer.render(
                        data=chart_config['data'],
                        chart_type=chart_config.get('type', 'line'),
                        title=chart_config.get('title', ''),
                        config=chart_config.get('config', {}),
                        key=f"{key}_{i}"
                    )
        
        elif layout == "rows":
            for i, chart_config in enumerate(charts):
                ChartContainer.render(
                    data=chart_config['data'],
                    chart_type=chart_config.get('type', 'line'),
                    title=chart_config.get('title', ''),
                    config=chart_config.get('config', {}),
                    key=f"{key}_{i}"
                )
        
        elif layout == "tabs":
            tab_names = [chart.get('title', f'图表{i+1}') for i, chart in enumerate(charts)]
            tabs = st.tabs(tab_names)
            
            for i, (tab, chart_config) in enumerate(zip(tabs, charts)):
                with tab:
                    ChartContainer.render(
                        data=chart_config['data'],
                        chart_type=chart_config.get('type', 'line'),
                        title=chart_config.get('title', ''),
                        config=chart_config.get('config', {}),
                        key=f"{key}_{i}"
                    )

    @staticmethod
    def dashboard_chart(
        data: pd.DataFrame,
        chart_configs: List[Dict[str, Any]],
        key: str = "dashboard_chart"
    ) -> None:
        """仪表盘图表布局"""
        # 创建网格布局
        for i in range(0, len(chart_configs), 2):
            cols = st.columns(2)
            
            # 第一列
            if i < len(chart_configs):
                config = chart_configs[i]
                with cols[0]:
                    ChartContainer.render(
                        data=data,
                        chart_type=config.get('type', 'line'),
                        title=config.get('title', ''),
                        config=config.get('config', {}),
                        height=config.get('height', 300),
                        key=f"{key}_{i}"
                    )
            
            # 第二列
            if i + 1 < len(chart_configs):
                config = chart_configs[i + 1]
                with cols[1]:
                    ChartContainer.render(
                        data=data,
                        chart_type=config.get('type', 'line'),
                        title=config.get('title', ''),
                        config=config.get('config', {}),
                        height=config.get('height', 300),
                        key=f"{key}_{i+1}"
                    )

    @staticmethod
    def real_time_chart(
        data_source: callable,
        chart_type: str = "line",
        title: str = "实时数据",
        update_interval: int = 5,
        key: str = "realtime_chart"
    ) -> None:
        """实时图表"""
        # 创建占位符
        chart_placeholder = st.empty()
        
        # 获取数据并更新图表
        try:
            data = data_source()
            
            with chart_placeholder.container():
                ChartContainer.render(
                    data=data,
                    chart_type=chart_type,
                    title=f"{title} (更新时间: {pd.Timestamp.now().strftime('%H:%M:%S')})",
                    key=f"{key}_realtime"
                )
        except Exception as e:
            Text.error(f"实时数据获取失败: {e}")
