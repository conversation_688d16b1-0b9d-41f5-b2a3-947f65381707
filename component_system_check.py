#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
组件系统检查工具

模块描述: 专门检查新的模块化组件系统是否正常工作
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s'
)
logger = logging.getLogger(__name__)

class ComponentSystemChecker:
    """组件系统检查器"""

    def __init__(self):
        self.results = {
            "atoms": {"status": "未检查", "details": {}},
            "molecules": {"status": "未检查", "details": {}},
            "organisms": {"status": "未检查", "details": {}},
            "integration": {"status": "未检查", "details": {}},
            "demo": {"status": "未检查", "details": {}}
        }

    def check_atoms(self):
        """检查原子组件"""
        logger.info("检查原子组件...")

        atom_components = [
            ("Button", "components.atoms.button", "Button"),
            ("Input", "components.atoms.input", "Input"),
            ("Text", "components.atoms.text", "Text"),
            ("Icon", "components.atoms.icon", "Icon"),
            ("Loading", "components.atoms.loading", "Loading")
        ]

        atom_results = {}
        all_passed = True

        for name, module_path, class_name in atom_components:
            try:
                module = __import__(module_path, fromlist=[class_name])
                component_class = getattr(module, class_name)

                # 检查是否有render方法或静态方法
                has_methods = any(
                    callable(getattr(component_class, attr, None))
                    for attr in dir(component_class)
                    if not attr.startswith('_')
                )

                if has_methods:
                    atom_results[name] = {"status": "通过", "details": "组件可导入且有可用方法"}
                    logger.info(f"✅ {name} 组件检查通过")
                else:
                    atom_results[name] = {"status": "失败", "details": "组件无可用方法"}
                    logger.error(f"❌ {name} 组件检查失败: 无可用方法")
                    all_passed = False

            except Exception as e:
                atom_results[name] = {"status": "错误", "details": str(e)}
                logger.error(f"❌ {name} 组件检查错误: {e}")
                all_passed = False

        self.results["atoms"]["details"] = atom_results
        self.results["atoms"]["status"] = "通过" if all_passed else "失败"

        return all_passed

    def check_molecules(self):
        """检查分子组件"""
        logger.info("检查分子组件...")

        molecule_components = [
            ("SearchBox", "components.molecules.search_box", "SearchBox"),
            ("FilterPanel", "components.molecules.filter_panel", "FilterPanel"),
            ("MetricCard", "components.molecules.metric_card", "MetricCard"),
            ("DataTable", "components.molecules.data_table", "DataTable"),
            ("ChartContainer", "components.molecules.chart_container", "ChartContainer")
        ]

        molecule_results = {}
        all_passed = True

        for name, module_path, class_name in molecule_components:
            try:
                module = __import__(module_path, fromlist=[class_name])
                component_class = getattr(module, class_name)

                # 检查是否有render方法
                has_render = hasattr(component_class, 'render') and callable(getattr(component_class, 'render'))

                if has_render:
                    molecule_results[name] = {"status": "通过", "details": "组件可导入且有render方法"}
                    logger.info(f"✅ {name} 组件检查通过")
                else:
                    molecule_results[name] = {"status": "失败", "details": "组件无render方法"}
                    logger.error(f"❌ {name} 组件检查失败: 无render方法")
                    all_passed = False

            except Exception as e:
                molecule_results[name] = {"status": "错误", "details": str(e)}
                logger.error(f"❌ {name} 组件检查错误: {e}")
                all_passed = False

        self.results["molecules"]["details"] = molecule_results
        self.results["molecules"]["status"] = "通过" if all_passed else "失败"

        return all_passed

    def check_organisms(self):
        """检查有机体组件"""
        logger.info("检查有机体组件...")

        organism_components = [
            ("AnalysisPanel", "components.organisms.analysis_panel", "AnalysisPanel"),
            ("DashboardHeader", "components.organisms.dashboard_header", "DashboardHeader"),
            ("NavigationSidebar", "components.organisms.navigation_sidebar", "NavigationSidebar"),
            ("ReportViewer", "components.organisms.report_viewer", "ReportViewer")
        ]

        organism_results = {}
        all_passed = True

        for name, module_path, class_name in organism_components:
            try:
                module = __import__(module_path, fromlist=[class_name])
                component_class = getattr(module, class_name)

                # 检查是否有render方法
                has_render = hasattr(component_class, 'render') and callable(getattr(component_class, 'render'))

                if has_render:
                    organism_results[name] = {"status": "通过", "details": "组件可导入且有render方法"}
                    logger.info(f"✅ {name} 组件检查通过")
                else:
                    organism_results[name] = {"status": "失败", "details": "组件无render方法"}
                    logger.error(f"❌ {name} 组件检查失败: 无render方法")
                    all_passed = False

            except Exception as e:
                organism_results[name] = {"status": "错误", "details": str(e)}
                logger.error(f"❌ {name} 组件检查错误: {e}")
                all_passed = False

        self.results["organisms"]["details"] = organism_results
        self.results["organisms"]["status"] = "通过" if all_passed else "失败"

        return all_passed

    def check_integration(self):
        """检查组件集成"""
        logger.info("检查组件集成...")

        integration_tests = []

        # 测试原子组件包导入
        try:
            from components.atoms import Button, Input, Text, Icon, Loading
            integration_tests.append(("原子组件包导入", "通过", "所有原子组件可从包导入"))
            logger.info("✅ 原子组件包导入成功")
        except Exception as e:
            integration_tests.append(("原子组件包导入", "失败", str(e)))
            logger.error(f"❌ 原子组件包导入失败: {e}")

        # 测试分子组件包导入
        try:
            from components.molecules import SearchBox, FilterPanel, MetricCard, DataTable, ChartContainer
            integration_tests.append(("分子组件包导入", "通过", "所有分子组件可从包导入"))
            logger.info("✅ 分子组件包导入成功")
        except Exception as e:
            integration_tests.append(("分子组件包导入", "失败", str(e)))
            logger.error(f"❌ 分子组件包导入失败: {e}")

        # 测试有机体组件包导入
        try:
            from components.organisms import AnalysisPanel, DashboardHeader, NavigationSidebar, ReportViewer
            integration_tests.append(("有机体组件包导入", "通过", "所有有机体组件可从包导入"))
            logger.info("✅ 有机体组件包导入成功")
        except Exception as e:
            integration_tests.append(("有机体组件包导入", "失败", str(e)))
            logger.error(f"❌ 有机体组件包导入失败: {e}")

        # 测试组件依赖
        try:
            from components.molecules.search_box import SearchBox
            from components.atoms.input import Input
            from components.atoms.button import Button

            # 检查SearchBox是否能正确使用原子组件
            integration_tests.append(("组件依赖", "通过", "分子组件可正确使用原子组件"))
            logger.info("✅ 组件依赖检查通过")
        except Exception as e:
            integration_tests.append(("组件依赖", "失败", str(e)))
            logger.error(f"❌ 组件依赖检查失败: {e}")

        # 更新结果
        self.results["integration"]["details"] = {
            test[0]: {"status": test[1], "details": test[2]}
            for test in integration_tests
        }

        all_passed = all(test[1] == "通过" for test in integration_tests)
        self.results["integration"]["status"] = "通过" if all_passed else "失败"

        return all_passed

    def check_demo(self):
        """检查演示应用"""
        logger.info("检查演示应用...")

        demo_checks = []

        # 检查演示文件是否存在
        demo_file = "component_demo.py"
        if os.path.exists(demo_file):
            demo_checks.append(("演示文件存在", "通过", f"{demo_file} 文件存在"))
            logger.info(f"✅ {demo_file} 文件存在")
        else:
            demo_checks.append(("演示文件存在", "失败", f"{demo_file} 文件不存在"))
            logger.error(f"❌ {demo_file} 文件不存在")

        # 检查启动脚本是否存在
        bat_file = "run_component_demo.bat"
        if os.path.exists(bat_file):
            demo_checks.append(("启动脚本存在", "通过", f"{bat_file} 文件存在"))
            logger.info(f"✅ {bat_file} 文件存在")
        else:
            demo_checks.append(("启动脚本存在", "失败", f"{bat_file} 文件不存在"))
            logger.error(f"❌ {bat_file} 文件不存在")

        # 检查测试脚本是否存在
        test_file = "test_components.py"
        if os.path.exists(test_file):
            demo_checks.append(("测试脚本存在", "通过", f"{test_file} 文件存在"))
            logger.info(f"✅ {test_file} 文件存在")
        else:
            demo_checks.append(("测试脚本存在", "失败", f"{test_file} 文件不存在"))
            logger.error(f"❌ {test_file} 文件不存在")

        # 检查有机体组件演示文件
        organism_demo_file = "organism_demo.py"
        if os.path.exists(organism_demo_file):
            demo_checks.append(("有机体演示文件存在", "通过", f"{organism_demo_file} 文件存在"))
            logger.info(f"✅ {organism_demo_file} 文件存在")
        else:
            demo_checks.append(("有机体演示文件存在", "失败", f"{organism_demo_file} 文件不存在"))
            logger.error(f"❌ {organism_demo_file} 文件不存在")

        # 检查有机体组件启动脚本
        organism_bat_file = "run_organism_demo.bat"
        if os.path.exists(organism_bat_file):
            demo_checks.append(("有机体启动脚本存在", "通过", f"{organism_bat_file} 文件存在"))
            logger.info(f"✅ {organism_bat_file} 文件存在")
        else:
            demo_checks.append(("有机体启动脚本存在", "失败", f"{organism_bat_file} 文件不存在"))
            logger.error(f"❌ {organism_bat_file} 文件不存在")

        # 更新结果
        self.results["demo"]["details"] = {
            check[0]: {"status": check[1], "details": check[2]}
            for check in demo_checks
        }

        all_passed = all(check[1] == "通过" for check in demo_checks)
        self.results["demo"]["status"] = "通过" if all_passed else "失败"

        return all_passed

    def check_all(self):
        """执行所有检查"""
        logger.info("🚀 开始组件系统全面检查...")

        # 执行各项检查
        atoms_ok = self.check_atoms()
        molecules_ok = self.check_molecules()
        organisms_ok = self.check_organisms()
        integration_ok = self.check_integration()
        demo_ok = self.check_demo()

        # 计算总体状态
        overall_ok = atoms_ok and molecules_ok and organisms_ok and integration_ok and demo_ok
        overall_status = "通过" if overall_ok else "失败"

        # 生成报告
        report = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "overall_status": overall_status,
            "summary": {
                "atoms": self.results["atoms"]["status"],
                "molecules": self.results["molecules"]["status"],
                "organisms": self.results["organisms"]["status"],
                "integration": self.results["integration"]["status"],
                "demo": self.results["demo"]["status"]
            },
            "details": self.results
        }

        # 打印摘要
        print("\n" + "=" * 60)
        print("🧩 模块化组件系统检查报告")
        print("=" * 60)
        print(f"检查时间: {report['timestamp']}")
        print(f"总体状态: {'🎉 通过' if overall_ok else '❌ 失败'}")
        print("-" * 60)
        print(f"原子组件: {'✅ 通过' if atoms_ok else '❌ 失败'}")
        print(f"分子组件: {'✅ 通过' if molecules_ok else '❌ 失败'}")
        print(f"有机体组件: {'✅ 通过' if organisms_ok else '❌ 失败'}")
        print(f"组件集成: {'✅ 通过' if integration_ok else '❌ 失败'}")
        print(f"演示应用: {'✅ 通过' if demo_ok else '❌ 失败'}")
        print("=" * 60)

        if overall_ok:
            print("🎉 恭喜！模块化组件系统检查全部通过！")
            print("📝 组件系统已准备就绪，可以开始使用。")
            print("🚀 运行 'run_component_demo.bat' 查看演示应用。")
        else:
            print("⚠️ 发现问题，请检查上述失败项。")

        print("=" * 60)

        return report

def main():
    """主函数"""
    checker = ComponentSystemChecker()
    report = checker.check_all()

    # 保存报告
    import json
    with open("component_system_check_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    logger.info("检查报告已保存到: component_system_check_report.json")

    return report["overall_status"] == "通过"

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
